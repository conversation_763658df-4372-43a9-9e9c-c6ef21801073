<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDebtActivityLogsTable extends Migration
{
    /**
     * Run the migrations.
     * 
     * Tạo bảng log cho các hoạt động liên quan đến debt
     *
     * @return void
     */
    public function up()
    {
        Schema::create('debt_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('debt_id')->comment('ID công nợ');
            $table->string('user_code', 20)->comment('Mã sinh viên');
            $table->string('action', 50)->comment('Hành động: create, update, cancel, payment, restore');
            $table->string('actor', 50)->comment('Người thực hiện');
            $table->string('actor_type', 20)->default('admin')->comment('<PERSON>ại người thực hiện: admin, system, student');
            $table->json('old_data')->nullable()->comment('<PERSON><PERSON> liệu cũ (JSON)');
            $table->json('new_data')->nullable()->comment('Dữ liệu mới (JSON)');
            $table->json('changes')->nullable()->comment('Các thay đổi cụ thể (JSON)');
            $table->text('description')->nullable()->comment('Mô tả chi tiết');
            $table->string('ip_address', 45)->nullable()->comment('Địa chỉ IP');
            $table->string('user_agent')->nullable()->comment('User Agent');
            $table->timestamp('created_at')->useCurrent();

            // Indexes
            $table->index('debt_id');
            $table->index('user_code');
            $table->index('action');
            $table->index('actor');
            $table->index('created_at');
            $table->index(['debt_id', 'action']);
            $table->index(['user_code', 'created_at']);

            // Foreign key constraints
            $table->foreign('debt_id')->references('id')->on('student_debts')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('debt_activity_logs');
    }
}
