<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveRedundantFeeDetails extends Migration
{
    /**
     * Run the migrations.
     * 
     * Bỏ bảng fee_details vì không cần thiết:
     * - <PERSON><PERSON><PERSON> hết thông tin đều duplicate với student_debts
     * - <PERSON><PERSON> thể dùng JOIN để lấy thông tin cần thiết
     * - Giảm complexity và tăng performance
     *
     * @return void
     */
    public function up()
    {
        // Trước khi xóa, migrate dữ liệu cần thiết vào student_debts
        // Thêm các trường từ fee_details vào student_debts nếu cần
        Schema::table('student_debts', function (Blueprint $table) {
            $table->tinyInteger('type_fee')->nullable()->after('fee_type_id')->comment('Loại phí chi tiết: 1=<PERSON><PERSON><PERSON>, 2=<PERSON><PERSON><PERSON>, 3-4=Tiếng anh, 5=Bổ sung');
            $table->integer('ki_thu')->default(1)->after('term_id')->comment('<PERSON><PERSON> thứ');
            $table->integer('version')->default(1)->after('discount_reason')->comment('Phiên bản');
        });

        // Migrate dữ liệu từ fee_details sang student_debts
        DB::statement("
            UPDATE student_debts 
            SET type_fee = (
                SELECT type_fee 
                FROM fee_details 
                WHERE fee_details.debt_id = student_debts.id 
                LIMIT 1
            ),
            ki_thu = (
                SELECT ki_thu 
                FROM fee_details 
                WHERE fee_details.debt_id = student_debts.id 
                LIMIT 1
            ),
            version = (
                SELECT version 
                FROM fee_details 
                WHERE fee_details.debt_id = student_debts.id 
                LIMIT 1
            )
            WHERE EXISTS (
                SELECT 1 
                FROM fee_details 
                WHERE fee_details.debt_id = student_debts.id
            )
        ");

        // Xóa bảng fee_details
        Schema::dropIfExists('fee_details');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Tạo lại bảng fee_details
        Schema::create('fee_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fee_id')->nullable()->comment('ID phí chính (nếu có)');
            $table->unsignedBigInteger('debt_id')->nullable()->comment('ID công nợ sinh viên');
            $table->string('user_code', 20)->comment('Mã sinh viên');
            $table->string('user_login', 50)->comment('Tài khoản đăng nhập');
            $table->unsignedBigInteger('term_id')->comment('ID kỳ học');
            $table->integer('ki_thu')->default(1)->comment('Kỳ thứ');
            $table->tinyInteger('type_fee')->comment('Loại phí: 1=Học kỳ, 2=Sách, 3-4=Tiếng anh, 5=Bổ sung');
            $table->decimal('original_amount', 15, 2)->comment('Số tiền gốc');
            $table->decimal('amount', 15, 2)->comment('Số tiền sau discount');
            $table->decimal('discount', 15, 2)->default(0)->comment('Giảm giá');
            $table->decimal('discount_percentage', 5, 2)->default(0)->comment('Phần trăm giảm giá');
            $table->string('discount_reason')->nullable()->comment('Lý do giảm giá');
            $table->integer('version')->default(1)->comment('Phiên bản');
            $table->timestamps();

            // Indexes
            $table->index('debt_id');
            $table->index('user_code');
            $table->index('term_id');
            $table->index('type_fee');

            // Foreign key constraints
            $table->foreign('debt_id')->references('id')->on('student_debts')->onDelete('cascade');
        });

        // Migrate dữ liệu ngược lại
        DB::statement("
            INSERT INTO fee_details (debt_id, user_code, user_login, term_id, ki_thu, type_fee, original_amount, amount, discount, discount_percentage, discount_reason, version, created_at, updated_at)
            SELECT id, user_code, user_login, term_id, ki_thu, type_fee, original_amount, amount, discount_amount, discount_percentage, discount_reason, version, created_at, updated_at
            FROM student_debts
        ");

        // Bỏ các trường đã thêm vào student_debts
        Schema::table('student_debts', function (Blueprint $table) {
            $table->dropColumn(['type_fee', 'ki_thu', 'version']);
        });
    }
}
