<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ImproveDiscountStructure extends Migration
{
    /**
     * Run the migrations.
     * 
     * Cải thiện cấu trúc discount:
     * 1. Thêm original_amount (số tiền gốc) vào fee_details
     * 2. Thêm original_amount (số tiền gốc) vào student_debts
     * 3. amount sẽ là số tiền sau discount (số tiền thực tế phải trả)
     * 4. Thêm discount_percentage để lưu % giảm giá
     *
     * @return void
     */
    public function up()
    {
        // Cập nhật bảng fee_details
        Schema::table('fee_details', function (Blueprint $table) {
            // Thêm cột original_amount (số tiền gốc trước discount)
            $table->decimal('original_amount', 15, 2)->after('type_fee')->comment('Số tiền gốc (trước discount)');
            
            // Thêm discount_percentage
            $table->decimal('discount_percentage', 5, 2)->default(0)->after('discount')->comment('Phần trăm giảm giá (0-100)');
            
            // Thêm discount_reason
            $table->string('discount_reason')->nullable()->after('discount_percentage')->comment('Lý do giảm giá');
        });

        // Cập nhật bảng student_debts
        Schema::table('student_debts', function (Blueprint $table) {
            // Thêm cột original_amount (số tiền gốc trước discount)
            $table->decimal('original_amount', 15, 2)->after('fee_type_id')->comment('Số tiền gốc (trước discount)');
            
            // Thêm discount_amount
            $table->decimal('discount_amount', 15, 2)->default(0)->after('original_amount')->comment('Số tiền được giảm');
            
            // Thêm discount_percentage
            $table->decimal('discount_percentage', 5, 2)->default(0)->after('discount_amount')->comment('Phần trăm giảm giá (0-100)');
            
            // Thêm discount_reason
            $table->string('discount_reason')->nullable()->after('discount_percentage')->comment('Lý do giảm giá');
        });

        // Migrate dữ liệu hiện tại an toàn
        // Đặt mặc định discount_percentage = 0
        DB::statement('UPDATE fee_details SET discount_percentage = 0 WHERE discount_percentage IS NULL');
        DB::statement('UPDATE student_debts SET discount_percentage = 0 WHERE discount_percentage IS NULL');

        // Tính original_amount từ amount hiện tại + discount
        DB::statement('UPDATE fee_details SET original_amount = amount + COALESCE(discount, 0) WHERE original_amount IS NULL');
        DB::statement('UPDATE student_debts SET original_amount = amount, discount_amount = 0 WHERE original_amount IS NULL');

        // Thêm indexes
        Schema::table('fee_details', function (Blueprint $table) {
            $table->index('original_amount');
            $table->index('discount_percentage');
        });

        Schema::table('student_debts', function (Blueprint $table) {
            $table->index('original_amount');
            $table->index('discount_amount');
            $table->index('discount_percentage');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fee_details', function (Blueprint $table) {
            $table->dropIndex(['original_amount']);
            $table->dropIndex(['discount_percentage']);
            $table->dropColumn(['original_amount', 'discount_percentage', 'discount_reason']);
        });

        Schema::table('student_debts', function (Blueprint $table) {
            $table->dropIndex(['original_amount']);
            $table->dropIndex(['discount_amount']);
            $table->dropIndex(['discount_percentage']);
            $table->dropColumn(['original_amount', 'discount_amount', 'discount_percentage', 'discount_reason']);
        });
    }
}
