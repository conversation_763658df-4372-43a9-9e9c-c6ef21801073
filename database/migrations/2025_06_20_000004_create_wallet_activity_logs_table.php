<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWalletActivityLogsTable extends Migration
{
    /**
     * Run the migrations.
     * 
     * Tạo bảng log cho các hoạt động liên quan đến wallet
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wallet_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('wallet_id')->nullable()->comment('ID ví (nếu có)');
            $table->string('user_code', 20)->comment('Mã sinh viên');
            $table->string('action', 50)->comment('Hành động: create, lock, unlock, update_balance, change_info');
            $table->string('actor', 50)->comment('Người thực hiện');
            $table->string('actor_type', 20)->default('admin')->comment('<PERSON><PERSON><PERSON> ng<PERSON>ời thực hiện: admin, system, student');
            $table->json('old_data')->nullable()->comment('<PERSON><PERSON> liệu cũ (JSON)');
            $table->json('new_data')->nullable()->comment('Dữ liệu mới (JSON)');
            $table->json('changes')->nullable()->comment('Các thay đổi cụ thể (JSON)');
            $table->text('description')->nullable()->comment('Mô tả chi tiết');
            $table->decimal('amount_involved', 15, 2)->nullable()->comment('Số tiền liên quan (nếu có)');
            $table->string('transaction_reference')->nullable()->comment('Tham chiếu giao dịch');
            $table->string('ip_address', 45)->nullable()->comment('Địa chỉ IP');
            $table->string('user_agent')->nullable()->comment('User Agent');
            $table->timestamp('created_at')->useCurrent();

            // Indexes
            $table->index('wallet_id');
            $table->index('user_code');
            $table->index('action');
            $table->index('actor');
            $table->index('created_at');
            $table->index(['user_code', 'action']);
            $table->index(['user_code', 'created_at']);
            $table->index('transaction_reference');

            // Foreign key constraints (nếu có bảng student_wallets)
            // $table->foreign('wallet_id')->references('id')->on('student_wallets')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wallet_activity_logs');
    }
}
