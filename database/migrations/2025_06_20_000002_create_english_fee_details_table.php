<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEnglishFeeDetailsTable extends Migration
{
    /**
     * Run the migrations.
     * 
     * Tạo bảng riêng cho thông tin English Level của các phí tiếng anh
     * Chỉ áp dụng cho type_fee = 3,4 (Tiếng anh)
     *
     * @return void
     */
    public function up()
    {
        Schema::create('english_fee_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fee_detail_id')->comment('ID fee detail');
            $table->unsignedTinyInteger('english_level_id')->comment('ID level tiếng anh');
            $table->string('english_level', 10)->comment('Level tiếng anh (A1, A2, B1, B2, C1, C2)');
            $table->decimal('level_fee', 15, 2)->default(0)->comment('Phí theo level');
            $table->text('notes')->nullable()->comment('Ghi chú');
            $table->timestamps();

            // Indexes
            $table->index('fee_detail_id');
            $table->index('english_level_id');
            $table->index('english_level');

            // Foreign key constraints
            $table->foreign('fee_detail_id')->references('id')->on('fee_details')->onDelete('cascade');
            
            // Unique constraint - mỗi fee_detail chỉ có 1 english level
            $table->unique('fee_detail_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('english_fee_details');
    }
}
