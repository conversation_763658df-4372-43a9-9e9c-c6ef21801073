<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class OptimizeFeeDetailsTable extends Migration
{
    /**
     * Run the migrations.
     * 
     * Tối ưu hóa bảng fee_details:
     * 1. Bỏ trường status (thừa - có thể lấy từ student_debts)
     * 2. Bỏ trường english_level (không liên quan đến tất cả loại phí)
     * 3. Thêm indexes cần thiết
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fee_details', function (Blueprint $table) {
            // Bỏ trường status vì có thể JOIN với student_debts để lấy
            $table->dropColumn('status');
            
            // Bỏ các trường english_level vì không liên quan đến tất cả loại phí
            $table->dropColumn(['english_level_id', 'english_level']);
        });

        // Thêm indexes để tối ưu performance
        Schema::table('fee_details', function (Blueprint $table) {
            // Composite index cho các query thường dùng
            $table->index(['user_code', 'term_id']);
            $table->index(['debt_id', 'type_fee']);
            $table->index(['term_id', 'type_fee']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fee_details', function (Blueprint $table) {
            // Thêm lại các trường đã bỏ
            $table->tinyInteger('status')->default(0)->comment('Trạng thái: 0=Chưa thanh toán, 1=Đã thanh toán');
            $table->unsignedTinyInteger('english_level_id')->nullable()->comment('ID level tiếng anh');
            $table->string('english_level', 10)->nullable()->comment('Level tiếng anh');
            
            // Bỏ indexes đã thêm
            $table->dropIndex(['user_code', 'term_id']);
            $table->dropIndex(['debt_id', 'type_fee']);
            $table->dropIndex(['term_id', 'type_fee']);
        });
    }
}
