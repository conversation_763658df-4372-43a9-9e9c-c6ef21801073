<?php

namespace App\Repositories\Admin;

use App\Models\Fee\StudentDebt;
use App\Models\Fee\DebtPayment;
use App\Models\Fee\StudentWallet;
// use App\Models\Fee\FeeDetail; // Không cần nữa vì đã bỏ bảng fee_details
use App\Models\Fee\WalletTransaction;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Fu\Term;
use App\Models\Fee\FeeType;

class StudentDebtRepository extends BaseRepository
{
    // Trạng thái công nợ
    const DEBT_STATUS_PENDING = 0;  // Chưa thanh toán
    const DEBT_STATUS_PAID = 1;     // Đã thanh toán
    const DEBT_STATUS_CANCELED = 2; // Đã hủy

    public function getModel()
    {
        return StudentDebt::class;
    }

    public function getDebtList($request)
    {
        $keyword = $request->keyword;
        $status = $request->status;
        $term_id = $request->term_id;

        $debts = StudentDebt::when($keyword, function ($query, $keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('user_code', 'like', "%$keyword%")
                    ->orWhere('user_login', 'like', "%$keyword%");
            });
        })
            ->when($status !== null, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($term_id, function ($query, $term_id) {
                $query->where('term_id', $term_id);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $terms = Term::orderBy('id', 'desc')->get(['id', 'term_name']);


        return $this->view('fee.debt.index', [
            'debts' => $debts,
            'terms' => $terms,
            'keyword' => $keyword,
            'status' => $status,
            'term_id' => $term_id
        ]);
    }

    public function createDebt($request)
    {
        // Validate and clean input data
        $data = $this->validateDebtData($request);
        if (!$data['valid']) {
            return response()->json([
                'success' => false,
                'message' => $data['message']
            ], 422);
        }

        DB::beginTransaction();
        try {
            // Check for duplicate debt
            $existingDebt = StudentDebt::where('user_code', $data['user_code'])
                ->where('term_id', $data['term_id'])
                ->where('fee_type_id', $data['fee_type_id'])
                ->where('status', '!=', self::DEBT_STATUS_CANCELED)
                ->first();

            if ($existingDebt) {
                throw new \Exception('Công nợ đã tồn tại cho sinh viên này trong kỳ học và loại phí này');
            }

            // Create debt với tính toán discount chính xác
            $originalAmount = floatval($data['original_amount'] ?? $data['amount']);
            $discountAmount = floatval($data['discount_amount'] ?? 0);
            $discountPercentage = floatval($data['discount_percentage'] ?? 0);

            // Đảm bảo mặc định discount_percentage = 0
            if ($discountPercentage < 0 || $discountPercentage > 100) {
                $discountPercentage = 0;
            }

            // Tính số tiền cuối cùng (sau discount) - ưu tiên percentage
            $finalAmount = $originalAmount;
            if ($discountPercentage > 0) {
                // Tính theo phần trăm
                $discountAmount = round($originalAmount * ($discountPercentage / 100), 2);
                $finalAmount = $originalAmount - $discountAmount;
            } elseif ($discountAmount > 0) {
                // Tính theo số tiền cụ thể
                $finalAmount = $originalAmount - $discountAmount;
                $discountPercentage = 0; // Reset percentage khi dùng amount
            } else {
                // Không có discount
                $discountAmount = 0;
                $discountPercentage = 0;
            }

            // Map fee_type_id sang type_fee
            $typeFeeMapping = [
                1 => 1, // Học phí -> Phí học kỳ
                2 => 5, // Phí ký túc xá -> Bổ sung
                3 => 5, // Phí bảo hiểm -> Bổ sung
                4 => 1, // Phí học lại -> Phí học kỳ
                5 => 1, // Phí thi lại -> Phí học kỳ
                6 => 5, // Phí khác -> Bổ sung
            ];
            $typeFee = $typeFeeMapping[$data['fee_type_id']] ?? 5;

            $debt = StudentDebt::create([
                'user_code' => $data['user_code'],
                'user_login' => $data['user_login'],
                'term_id' => $data['term_id'],
                'term_name' => $data['term']->term_name,
                'fee_type_id' => $data['fee_type_id'],
                'fee_id' => $data['fee_id'] ?? null,
                'ki_thu' => $this->getKiThuFromTerm($data['term_id']),
                'type_fee' => $typeFee,
                'original_amount' => $originalAmount,
                'discount_amount' => $discountAmount,
                'discount_percentage' => $discountPercentage,
                'discount_reason' => $data['discount_reason'] ?? null,
                'amount' => $finalAmount, // Số tiền thực tế phải trả (sau discount)
                'paid_amount' => 0,
                'version' => 1,
                'status' => self::DEBT_STATUS_PENDING,
                'description' => $data['description'],
                'created_by' => Auth::user()->user_login ?? 'system'
            ]);

            // Không cần tạo FeeDetail nữa - tất cả thông tin đã có trong StudentDebt

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tạo công nợ thành công',
                'debt' => $debt
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create debt error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Tạo công nợ thất bại: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate debt creation data
     */
    private function validateDebtData($request)
    {
        $user_code = strtoupper(trim($request->user_code ?? ''));
        $user_login = trim($request->user_login ?? '');
        $term_id = $request->term_id;
        $fee_type_id = $request->fee_type_id;
        $amount = floatval($request->amount ?? 0);
        $original_amount = floatval($request->original_amount ?? $amount);
        $discount_amount = floatval($request->discount_amount ?? 0);
        $discount_percentage = floatval($request->discount_percentage ?? 0);
        $discount_reason = trim($request->discount_reason ?? '');
        $description = trim($request->description ?? '');

        // Basic validation
        if (empty($user_code)) {
            return ['valid' => false, 'message' => 'Mã sinh viên không được để trống'];
        }
        if (empty($user_login)) {
            return ['valid' => false, 'message' => 'Tài khoản đăng nhập không được để trống'];
        }
        if ($original_amount <= 0) {
            return ['valid' => false, 'message' => 'Số tiền gốc phải lớn hơn 0'];
        }

        // Validate discount chặt chẽ
        if ($discount_percentage < 0 || $discount_percentage > 100) {
            return ['valid' => false, 'message' => 'Phần trăm giảm giá phải từ 0 đến 100'];
        }
        if ($discount_amount < 0 || $discount_amount > $original_amount) {
            return ['valid' => false, 'message' => 'Số tiền giảm giá không hợp lệ (0 - ' . number_format($original_amount) . ')'];
        }
        if ($discount_percentage > 0 && $discount_amount > 0) {
            return ['valid' => false, 'message' => 'Chỉ được chọn một trong hai: phần trăm giảm giá hoặc số tiền giảm giá'];
        }

        // Validate kết quả cuối
        $testFinalAmount = $original_amount;
        if ($discount_percentage > 0) {
            $testFinalAmount = $original_amount * (1 - $discount_percentage / 100);
        } elseif ($discount_amount > 0) {
            $testFinalAmount = $original_amount - $discount_amount;
        }

        if ($testFinalAmount <= 0) {
            return ['valid' => false, 'message' => 'Số tiền sau giảm giá phải lớn hơn 0'];
        }

        // Validate term
        $term = Term::find($term_id);
        if (!$term) {
            return ['valid' => false, 'message' => 'Kỳ học không tồn tại'];
        }

        // Validate fee type
        $feeType = FeeType::find($fee_type_id);
        if (!$feeType) {
            return ['valid' => false, 'message' => 'Loại phí không tồn tại'];
        }

        return [
            'valid' => true,
            'user_code' => $user_code,
            'user_login' => $user_login,
            'term_id' => $term_id,
            'term' => $term,
            'fee_type_id' => $fee_type_id,
            'fee_type' => $feeType,
            'amount' => $amount,
            'original_amount' => $original_amount,
            'discount_amount' => $discount_amount,
            'discount_percentage' => $discount_percentage,
            'discount_reason' => $discount_reason,
            'description' => $description
        ];
    }

    public function cancelDebt($request, $id)
    {
        try {
            $debt = StudentDebt::findOrFail($id);
            $reason = trim($request->reason ?? '');

            if ($debt->status == self::DEBT_STATUS_PAID) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể hủy công nợ đã thanh toán'
                ], 422);
            }

            if ($debt->status == self::DEBT_STATUS_CANCELED) {
                return response()->json([
                    'success' => false,
                    'message' => 'Công nợ đã được hủy trước đó'
                ], 422);
            }

            DB::beginTransaction();

            $debt->status = self::DEBT_STATUS_CANCELED;
            $debt->canceled_by = Auth::user()->user_login ?? 'system';
            $debt->canceled_at = now();
            $debt->description = $debt->description . ($reason ? " (Lý do hủy: {$reason})" : '');
            $debt->save();

            // Log cancellation activity
            $debt->logCancellation($reason);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Hủy công nợ thành công',
                'debt' => $debt
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cancel debt error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Hủy công nợ thất bại: ' . $e->getMessage()
            ], 500);
        }
    }

    public function paymentForm($id)
    {
        $debt = StudentDebt::findOrFail($id);

        if ($debt->status != self::DEBT_STATUS_PENDING) {
            return $this->redirectWithStatus('danger', 'Chỉ có thể thanh toán công nợ đang chờ', route('admin.debt.list'));
        }

        // Kiểm tra ví sinh viên
        $wallet = StudentWallet::where('user_code', $debt->user_code)->first();

        return $this->view('fee.debt.payment', [
            'debt' => $debt,
            'wallet' => $wallet
        ]);
    }

    public function processPayment($request, $id)
    {
        $debt = StudentDebt::findOrFail($id);
        $payment_type = $request->payment_type; // 'wallet' hoặc 'direct'
        $wallet_type = $request->wallet_type;   // Nếu payment_type là 'wallet'
        $amount = floatval($request->amount);
        $invoice_id = $request->invoice_id;
        $payment_method = $request->payment_method;
        $note = $request->note;

        if ($debt->status != self::DEBT_STATUS_PENDING) {
            return $this->redirectWithStatus('danger', 'Chỉ có thể thanh toán công nợ đang chờ', route('admin.debt.list'));
        }

        if ($amount <= 0 || $amount > ($debt->amount - $debt->paid_amount)) {
            return $this->redirectWithStatus('danger', 'Số tiền không hợp lệ', route('admin.debt.payment.form', $id));
        }

        DB::beginTransaction();
        try {
            if ($payment_type == 'wallet') {
                // Thanh toán từ ví sinh viên
                $wallet = StudentWallet::where('user_code', $debt->user_code)->firstOrFail();

                if ($wallet->is_locked) {
                    throw new \Exception('Ví đã bị khóa');
                }

                // Kiểm tra số dư ví
                $balance = 0;
                switch ($wallet_type) {
                    case 'study':
                        $balance = $wallet->study_balance;
                        break;
                    case 'relearn':
                        $balance = $wallet->relearn_balance;
                        break;
                    case 'extra':
                        $balance = $wallet->extra_balance;
                        break;
                    case 'promotion':
                        $balance = $wallet->promotion_balance;
                        break;
                    default:
                        throw new \Exception('Loại ví không hợp lệ');
                }

                if ($balance < $amount) {
                    throw new \Exception('Số dư ví không đủ');
                }

                // Trừ tiền trong ví
                $balance_before = $balance;
                switch ($wallet_type) {
                    case 'study':
                        $wallet->study_balance -= $amount;
                        $balance = $wallet->study_balance;
                        break;
                    case 'relearn':
                        $wallet->relearn_balance -= $amount;
                        $balance = $wallet->relearn_balance;
                        break;
                    case 'extra':
                        $wallet->extra_balance -= $amount;
                        $balance = $wallet->extra_balance;
                        break;
                    case 'promotion':
                        $wallet->promotion_balance -= $amount;
                        $balance = $wallet->promotion_balance;
                        break;
                }
                $wallet->save();

                // Tạo giao dịch ví
                WalletTransaction::create([
                    'user_code' => $wallet->user_code,
                    'user_login' => $wallet->user_login,
                    'wallet_type' => $wallet_type,
                    'transaction_type' => 'payment',
                    'amount' => $amount,
                    'balance_before' => $balance_before,
                    'balance_after' => $balance,
                    'reference_id' => $debt->id,
                    'description' => 'Thanh toán công nợ ' . $debt->term_name,
                    'created_by' => Auth::user()->user_login
                ]);
            }

            // Tạo giao dịch thanh toán công nợ
            DebtPayment::create([
                'debt_id' => $debt->id,
                'user_code' => $debt->user_code,
                'user_login' => $debt->user_login,
                'amount' => $amount,
                'payment_method' => $payment_method ?? ($payment_type == 'wallet' ? 'wallet' : null),
                'invoice_id' => $invoice_id,
                'note' => $note,
                'created_by' => Auth::user()->user_login
            ]);

            // Cập nhật công nợ
            $debt->paid_amount += $amount;

            // Kiểm tra nếu đã thanh toán đủ
            if ($debt->paid_amount >= $debt->amount) {
                $debt->status = self::DEBT_STATUS_PAID;
            }

            $debt->save();

            // Log payment activity
            $debt->logPayment($amount, $payment_method ?? ($payment_type == 'wallet' ? 'wallet' : null));

            DB::commit();
            return $this->redirectWithStatus('success', 'Thanh toán công nợ thành công', route('admin.debt.list'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment error: ' . $e->getMessage());
            return $this->redirectWithStatus('danger', 'Thanh toán công nợ thất bại: ' . $e->getMessage(), route('admin.debt.payment.form', $id));
        }
    }



    public function getDebtDetail($id)
    {
        try {
            // Lấy thông tin công nợ với thông tin liên quan
            $debt = StudentDebt::select('student_debts.*', 'term.term_name', 'fee_types.name as fee_type_name')
                ->leftJoin('term', 'student_debts.term_id', '=', 'term.id')
                ->leftJoin('fee_types', 'student_debts.fee_type_id', '=', 'fee_types.id')
                ->where('student_debts.id', $id)
                ->firstOrFail();

            // Lấy lịch sử thanh toán của công nợ
            $payments = DebtPayment::where('debt_id', $id)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'debt' => $debt,
                'payments' => $payments
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting debt detail: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải thông tin công nợ: ' . $e->getMessage()
            ], 500);
        }
    }

    // Method createFeeDetailForDebt đã bỏ vì không cần tạo FeeDetail nữa
    // Tất cả thông tin đã được lưu trực tiếp trong StudentDebt

    /**
     * Lấy ki_thu từ term_id (có thể customize logic này)
     */
    protected function getKiThuFromTerm($termId)
    {
        try {
            $term = Term::find($termId);
            if ($term && isset($term->ordering)) {
                return $term->ordering;
            }
            return 1; // Default
        } catch (\Exception $e) {
            return 1; // Default
        }
    }
}
