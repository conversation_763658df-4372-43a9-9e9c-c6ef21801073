<?php

namespace App\Repositories\Admin;

use App\Http\Lib;
use Carbon\Carbon;
use App\Models\Fu\User;
use App\Models\Fu\Term;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ScanningStudentExport;
use App\Models\Report\RP\ListStudent as ListStudentRP;
use App\Models\Report\FZ\ListStudent as ListStudentFZ;
use App\Models\Report\FZ\MappingTerm;
use App\Models\Fee\FeeStudyBySemester;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class ScanningStudentRepository extends BaseRepository
{
    public function getModel()
    {
        return ListStudentRP::class;
    }

    const LIST_SEMESTER = [0, 1, 2, 3, 4, 5, 6, 7, 8];
    const list_eliminated_subjects = [
        "MEC220","PRO105","PRO107","PRO108","PRO109","PRO110","PRO115","PRO116","PRO117","PRO118","PRO119",
        "PRO120","PRO121","PRO122","PRO123","PRO126","PRO128","PRO130","VIE104","ENT102","ENT111","ENT112",
        "ENT121","ENT122","ENT201","ENT211","ENT221","EHO101","EHO102","EHO201","EHO202","ETO101","ETO201",
        "ETO301","ETO401"
    ];

    /**
     * getListScanningStudent
     * ( get danh sách dự đoán trạng thái sinh viên )
     * @param  mixed $request
     * @return response
     */
    public function getListScanningStudent($request) {
        ini_set("memory_limit", "-1");
        ini_set('max_execution_time', "-1");
        try {
            $mapping_term = null;
            $list_student = [];
            $list_sutdent_change = [];
            $list_group_member = collect([]);
            $list_current_semester_subjects = collect([]);
            $list_study_status = [$request->study_status];
            $list_learning_status = config('status')->trang_thai_hoc;

            // get term, MappingTerm
            $list_term = Term::orderBy('startday', 'DESC')->get(['id', 'term_name']);
            $term_new = $list_term[0]->id;
            $term_id = $request->term_id == null ? $term_new : $request->term_id;
            $term_id_fz = $term_id;
            $current_term = Term::find($term_id_fz);


            // kiểm tra trạng thái vì vẫn lẫn trạng thái cũ ở dữ liệu
            if (isset($request->study_status) || $request->study_status != null) {
                switch ($request->study_status) {
                    case 'TN':
                        $list_study_status = [$request->study_status, 'TN1'];
                        break;
                    case 'HL':
                        $list_study_status = [$request->study_status, 'TN2'];
                        break;
                    case 'CXL':
                        $list_study_status = [$request->study_status, 'TN3'];
                        break;
                    case 'CTN':
                        $list_study_status = [$request->study_status, 'BB2'];
                        break;
                }
            }

            // get data kỳ cũ và kỳ mới
            if (!isset($request->term_id) || $term_new == $term_id) {
                $list_student = ListStudentRP::query();
            } else {
                $list_student = ListStudentFZ::query();
            }

            $list_student
            ->when(isset($request->study_status) || $request->study_status != null, function ($query) use($list_study_status) {
                $query->whereIn('status', $list_study_status);
            })
            ->when(isset($request->user_search) && $request->user_search != null, function ($query) use($request) {
                $query->where('student_code', 'like', "%$request->user_search%");
            })
            ->when(isset($request->semester) && $request->semester != null, function ($query) use($request) {
                $query->where('semester', $request->semester);
            })
            ->when(isset($request->term_id) && $term_new != $term_id, function ($query) use($term_id_fz) {
                $query->where('term_id', $term_id_fz);
            });

            // get data export excel và data view
            if (isset($request->exportExcel) && $request->exportExcel == 'exportExcel') {
                // limit data query
                $totalUser = $list_student->count();
                $listData = null;
                $limit = 500;
                $offset = ceil($totalUser / $limit);
                for ($i = 0; $i < $offset * $limit; $i += $limit) {
                    $result_query = $this->queryGetData($request, $list_student, $i, $limit, $term_id, $current_term);
                    if ($listData == null) {
                        $listData = $result_query;
                    } else {
                        $listData = $listData->merge($result_query);
                    }
                }
                $list_student = $listData;
            } else {
                // get data view
                $list_student = $this->queryGetData($request, $list_student, null, null, $term_id, $current_term);
            }

            foreach ($list_student as $student) {
                // khai báo dữ liệu mặc định cho sinh viên
                if (!isset($list_sutdent_change[$student->student_code])) {
                    $list_sutdent_change[$student->student_code]['user_code'] = $student->student_code;
                    $list_sutdent_change[$student->student_code]['study_status'] = $student->status;
                    $list_sutdent_change[$student->student_code]['semester'] = $student->semester;
                    $list_sutdent_change[$student->student_code]['complete_tuition_fee'] = false;
                    $list_sutdent_change[$student->student_code]['complete_tuition'] = false;
                    $list_sutdent_change[$student->student_code]['register_service'] = false;
                    $list_sutdent_change[$student->student_code]['register_turn_retest'] = false;
                    $list_sutdent_change[$student->student_code]['register_dropout'] = false;
                    $list_sutdent_change[$student->student_code]['register_reserve'] = false;
                    $list_sutdent_change[$student->student_code]['register_base_conversion'] = false;
                    $list_sutdent_change[$student->student_code]['slot_continue_learning'] = false;
                    $list_sutdent_change[$student->student_code]['turn_learn_again'] = false;
                    $list_sutdent_change[$student->student_code]['turn_retest'] = false;
                    $list_sutdent_change[$student->student_code]['status_true'] = 'KXD';
                }


                // group_member => tính lượt học của các môn, current_semester_subjects => các môn học của kỳ hiện tại
                foreach ($student->users as $users) {
                    $list_group_member = collect($users->group_member);
                    $list_current_semester_subjects = collect($users->current_semester_subjects);
                }

                // kiểm tra phí học kỳ
                foreach ($student->fee_study_by_semester as $fee_study) {
                    // completed = 1 => hoàn thành học phí
                    if ($fee_study->completed == 1) {
                        $list_sutdent_change[$student->student_code]['complete_tuition_fee'] = true;
                    }
                }

                // kiểm tra xem sinh viên này đã hoàn thành chương trình học hay chưa
                foreach ($student->transcript as $transcript) {
                    // details_count => tổng số môn cần học, total_learned_count => tổng số môn đã học
                    if ($transcript->details_count >= 1 && $transcript->details_count == $transcript->total_learned_count) {
                        $list_sutdent_change[$student->student_code]['complete_tuition'] = true;
                    }
                }


                // kiểm tra xem sinh viên có lượt học đi ở kỳ này hay ko
                if (count($list_group_member->where('luot_hoc', 1)->where('term_id', $term_id)) >= 1) {
                    $list_sutdent_change[$student->student_code]['slot_continue_learning'] = true;
                }

                // đếm lượt học lại của các môn tiếng anh, GDQP, thực tập
                foreach ($list_current_semester_subjects->whereIn('skill_code', self::list_eliminated_subjects) as $current_semester_subjects) {
                    // list_eliminated_subjects => những môn học ko có đơn học lại, luot_hoc >= 2 => có lượt học lại
                    if (count($list_group_member->where('luot_hoc', '>=', 2)->where('skill_code', $current_semester_subjects->skill_code)) >= 1) {
                        $list_sutdent_change[$student->student_code]['turn_learn_again'] = true;
                    }
                }

                $list_sutdent_change[$student->student_code]['status_true'] = $this->checkStudyStatusStudentPrediction($list_sutdent_change[$student->student_code]);
                $list_sutdent_change[$student->student_code]['complete_tuition_fee'] = $this->checkDataFeeNaN($student->fee_study_by_semester, $list_sutdent_change[$student->student_code]['complete_tuition_fee']);
            }

            // export excel
            if (isset($request->exportExcel) && $request->exportExcel == 'exportExcel') {
                $export = new ScanningStudentExport($list_sutdent_change);
                return Excel::download($export, 'danh_sach_sinh_vien_da_quet_' . Carbon::now() . '.xlsx');
            }
            return response()->json([
                'list_student' => $list_student,
                'list_sutdent_change' => $list_sutdent_change,
                'list_learning_status' => $list_learning_status,
                'list_semester' => self::LIST_SEMESTER,
                'list_term' => $list_term,
            ]);

        } catch (\Throwable $th) {
            Log::error("-----------------------start err getListScanningStudent-------------------");
            Log::error($th->getFile() . " - " . $th->getLine() . " : " . $th->getMessage());
            Log::error("-----------------------end err getListScanningStudent-------------------");
            return response("", 500);
        }
    }

    /**
     * queryGetData
     *
     * @param  mixed $request
     * @param  mixed $list_student ( query )
     * @param  mixed $skip ( số lượng bản ghi )
     * @param  mixed $limit ( giới hạn )
     * @param  mixed $term_id ( id kỳ )
     * @return collect
     */
    public function queryGetData($request, $list_student, $skip = null, $limit = null, $term_id, $current_term) {
        ini_set("memory_limit", "-1");
        ini_set('max_execution_time', "-1");
        try {
            $list_student
            ->when(isset($request->exportExcel) && $request->exportExcel == 'exportExcel', function($query) use($skip, $limit) {
                $query->skip($skip)->limit($limit);
            })
            ->with(['transcript' => function ($transcript) {
                $transcript
                ->withCount('details')
                ->withCount('totalLearned');
            }])
            ->with(['fee_study_by_semester' => function($fee_study) use($term_id) {
                $fee_study
                ->where('term_id', $term_id)
                ->select(['user_code','term_id','completed']);
            }])
            ->with(['users' => function ($users) use($term_id) {
                $users
                ->with(['group_member' => function ($group_member) use ($term_id) {
                    $group_member
                    ->join('list_group', 'list_group.id', '=', 'group_member.groupid')
                    ->where('list_group.pterm_id', '<=', $term_id)
                    ->where('list_group.is_virtual', 0)
                    ->select([
                        'list_group.id',
                        'list_group.skill_code',
                        'list_group.pterm_id as term_id',
                        'group_member.member_login',
                        DB::raw('count(*) as luot_hoc'),
                    ])
                    ->groupBy('group_member.member_login', 'list_group.skill_code')
                    ->orderby('list_group.id', 'DESC');
                }])
                ->with(['current_semester_subjects' => function ($current_semester_subjects) use($term_id) {
                    $current_semester_subjects
                    ->join('list_group', 'list_group.id', '=', 'group_member.groupid')
                    ->where('list_group.pterm_id', $term_id)
                    ->where('list_group.is_virtual', 0)
                    ->select([
                        'list_group.id',
                        'list_group.skill_code',
                        'list_group.pterm_id as term_id',
                        'group_member.member_login',
                    ])
                    ->orderBy('list_group.id', 'DESC');
                }])
                ->select(['user_login', 'user_code']);
            }])
            ->orderBy('id', 'DESC');

            if (isset($request->exportExcel) && $request->exportExcel == 'exportExcel') {
                $list_student = $list_student->get();
            } else {
                $list_student = $list_student->paginate(8);
            }

            return $list_student;
        } catch (\Throwable $th) {
            Log::error("-----------------------start err queryGetData-------------------");
            Log::error($th->getFile() . " - " . $th->getLine() . " : " . $th->getMessage());
            Log::error("-----------------------end err queryGetData-------------------");
            return [];
        }
    }

    /**
     * checkStudyStatusStudentPrediction - chia case ra để check xem
     *
     * @param  mixed $student(dữ liệu của sinh viên)
     * @return string text
    */
    public function checkStudyStatusStudentPrediction($student) {
        // trường hợp 13
        if(strtoupper($student['study_status']) == 'TNG' && $student['complete_tuition'] == true) {
            return 'TNG';
        }
        // trường hợp 1
        if ($student['complete_tuition'] == true && $student['register_dropout'] == false) {
            return 'CTN';
        }
        // trường hợp 12
        if($student['register_dropout'] == true) {
            return 'BH';
        }
        // trường hợp 3
        if
        (
            $student['register_reserve'] == true &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == false &&
            $student['turn_learn_again'] == false &&
            $student['turn_retest'] == false &&
            $student['register_dropout'] == false
        ) {
            return 'TN';
        }
        // trường hợp 4
        if
        (
            $student['complete_tuition_fee'] == true &&
            $student['register_reserve'] == false &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == true &&
            $student['register_dropout'] == false &&
            $student['semester'] <= 7 && $student['semester'] >= 0
        ) {
            return 'HD';
        }
        // trường hợp 5
        if
        (
            $student['complete_tuition_fee'] == false &&
            $student['complete_tuition'] == false &&
            $student['register_reserve'] == false &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == true &&
            $student['register_dropout'] == false
        ) {
            return 'HL';
        }
        // trường hợp 6
        if
        (
            $student['complete_tuition'] == false &&
            $student['register_reserve'] == false &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == false &&
            (
                $student['turn_learn_again'] == true ||
                $student['turn_retest'] == true
            )
            &&
            $student['register_dropout'] == false
        ) {
            return 'HL';
        }
        // trường hợp 7
        if
        (
            $student['semester'] == 8 &&
            $student['complete_tuition_fee'] == true &&
            $student['complete_tuition'] == false &&
            $student['register_dropout'] == false &&
            $student['register_reserve'] == false &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == true
        ) {
            return 'HL';
        }
        // trường hợp 8
        if
        (
            $student['complete_tuition_fee'] == true &&
            $student['complete_tuition'] == false &&
            (
                $student['register_service'] == true ||
                $student['register_turn_retest'] == true
            )
            &&
            $student['register_reserve'] == false &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == false &&
            $student['turn_learn_again'] == false &&
            $student['turn_retest'] == false &&
            $student['register_dropout'] == false
        ) {
            return 'CXL';
        }
        // trường hợp 9
        if
        (
            $student['complete_tuition_fee'] == true &&
            $student['complete_tuition'] == false &&
            $student['register_service'] == false &&
            $student['register_turn_retest'] == false &&
            $student['register_reserve'] == false &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == false &&
            $student['turn_learn_again'] == false &&
            $student['turn_retest'] == false &&
            $student['register_dropout'] == false
        ) {
            return 'CXL';
        }
        // trường hợp 10
        if
        (
            $student['complete_tuition_fee'] == false &&
            $student['complete_tuition'] == false &&
            (
                $student['register_service'] == true ||
                $student['register_turn_retest'] == true
            )
            &&
            $student['register_reserve'] == false &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == false &&
            $student['turn_learn_again'] == false &&
            $student['turn_retest'] == false &&
            $student['register_dropout'] == false
        ) {
            return 'CXL';
        }
        // trường hợp 11
        if
        (
            $student['complete_tuition_fee'] == false &&
            $student['register_service'] == false &&
            $student['register_turn_retest'] == false &&
            $student['register_reserve'] == false &&
            $student['register_base_conversion'] == false &&
            $student['slot_continue_learning'] == false &&
            $student['turn_learn_again'] == false &&
            $student['turn_retest'] == false &&
            $student['register_dropout'] == false
        ) {
            return 'BH';
        }
        return 'KXD';
    }

    /**
     * checkDataFeeNaN - kiểm tra phí của của sinh viên, xem đã hoàn thành hay chưa
     *
     * @param  mixed $fee(dữ liệu phí)
     * @return bool true, false, N/A
    */
    public function checkDataFeeNaN($fee, $status_fee)
    {
        if (count($fee) <= 0 || empty($fee)) {
            return 'N/A';
        }

        if ($status_fee === true) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * getDataImportFee
     *
     * @param  mixed $request
     * @return mixed $response
     */
    public function getDataImportFee($request) {
        $list_term = Term::orderBy('startday', 'DESC')->get(['id', 'term_name']);
        $list_import_fee = FeeStudyBySemester::query()
        ->when(isset($request->user_search) && $request->user_search != null, function($query) use($request) {
            $query->where('user_code', $request->user_search);
        })
        ->when(isset($request->term_id) && $request->term_id != null, function($query) use($request) {
            $query->where('term_id', $request->term_id);
        })
        ->join('term', 'term.id', 'fee_study_by_semester.term_id')
        ->select([
            'fee_study_by_semester.*',
            'term.term_name',
            DB::raw('(CASE WHEN completed = 0 THEN "Chưa hoàn thành" WHEN completed = 1 THEN "Đã hoàn thành" END) as status_fee'),
        ])
        ->orderBy('fee_study_by_semester.updated_at', 'DESC')
        ->paginate(8);
        return response()->json([
            'list_import_fee' => $list_import_fee,
            'list_term' => $list_term,
        ]);
    }

    /**
     * importFee - dùng để import phí sinh viên, phục vụ chính cho tool quét trạng thái sinh viên
     *
     * @param  mixed $request
     * @return mixed $response
     */
    public function importFee($request) {
        $list_report = [];
        $success = null;

        try {
            if (!$file = $this->importDataFromFile($request->file)) {
                $list_report[] = "Không có sinh viên nào để import hoặc file tải lên bị lỗi, vui lòng kiểm tra lại!";
            }

            $total_file = count($file);
            $limit = 1000;
            $offset = ceil($total_file / $limit);
            for ($i = 0; $i < $offset * $limit; $i += $limit) {
                $list_data = [];
                foreach (array_slice($file, $i, $limit) as $key => $item) {
                    $term_id = $request->term_id;
                    $user_code = trim($item[0]);
                    $completed = trim($item[1]);
                    $index = (int)$key + 2;

                    if ($completed != 0 && $completed != 1) {
                        $list_report[] = 'Dòng: ' . $index . " Dữ liệu chuyền vào thiếu hoặc không đúng, vui lòng kiểm tra lại!";
                        continue;
                    }
                    $student = User::where('user_code', 'like', "%$user_code%")->first();
                    if (empty($student)) {
                        $list_report[] = 'Dòng: ' . $index . " sinh viên " . $user_code . " không tồn tại , vui lòng kiểm tra lại";
                        continue;
                    }

                    $list_data[] = [
                        'user_code' => $student->user_code,
                        'term_id' => $term_id,
                        'completed' => $completed,
                        'last_modifier' => Auth::user()->user_login,
                    ];
                }

                if (!empty($list_data)) {
                    FeeStudyBySemester::upsert(
                        $list_data,
                        [
                            'user_code',
                            'term_id',
                        ],
                        [
                            'completed',
                            'last_modifier',
                        ]
                    );
                    $success = 'Import danh sách sinh viên đã đóng học phí thành công!!';
                }
            }

            return response()->json([
                'list_report' => $list_report,
                'success' => $success,
            ]);
        } catch (\Exception $th) {
            Log::error("-----------------------start err importFee-------------------");
            Log::error($th->getFile() . " - " . $th->getLine() . " : " . $th->getMessage());
            Log::error("-----------------------end err importFee-------------------");
            return response("", 500);
        }
    }
}
