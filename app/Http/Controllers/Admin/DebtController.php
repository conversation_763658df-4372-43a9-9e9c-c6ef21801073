<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\StudentDebtRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\DebtExport;
use App\Exports\SimpleExcelTemplate;
use App\Exports\DebtImportSampleExport;
use App\Imports\DebtBulkImport;
use Illuminate\Support\Facades\Log;
use App\Models\Fee\StudentDebt as Debt;
use App\Models\Fee\DebtPayment;
use App\Models\Fu\Term;
use Illuminate\Support\Facades\DB;

class DebtController extends Controller
{
    protected $debtRepository;

    public function __construct(StudentDebtRepository $debtRepository)
    {
        $this->debtRepository = $debtRepository;
    }

    /**
     * Show debt list page (for web routes)
     */
    public function index()
    {
        return view('admin_v1.fee.debt.index');
    }

    /**
     * Return debt list as JSON for API
     */
    public function list(Request $request)
    {
        try {
            // Query cơ bản với các trường mới từ fee_details
            $query = Debt::query()
                ->select(
                    'student_debts.*',
                    'term.term_name',
                    'fee_types.name as fee_type_name',
                    // Các trường mới đã được merge từ fee_details
                    'student_debts.type_fee',
                    'student_debts.ki_thu',
                    'student_debts.version',
                    'student_debts.original_amount',
                    'student_debts.discount_amount',
                    'student_debts.discount_percentage',
                    'student_debts.discount_reason'
                )
                ->leftJoin('term', 'student_debts.term_id', '=', 'term.id')
                ->leftJoin('fee_types', 'student_debts.fee_type_id', '=', 'fee_types.id');

            // Áp dụng các bộ lọc
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function ($q) use ($keyword) {
                    $q->where('student_debts.user_code', 'like', "%{$keyword}%")
                        ->orWhere('student_debts.user_name', 'like', "%{$keyword}%");
                });
            }

            if ($request->filled('term_id') && $request->input('term_id') != '') {
                $query->where('student_debts.term_id', $request->input('term_id'));
            }

            if ($request->filled('status') && $request->input('status') !== '') {
                $query->where('student_debts.status', $request->input('status'));
            }

            // Phân trang kết quả
            $perPage = $request->input('per_page', 10);
            $debts = $query->orderBy('student_debts.created_at', 'desc')
                ->paginate($perPage);

            // Chuyển đổi dữ liệu để phù hợp với giao diện
            $debts->getCollection()->transform(function ($debt) {
                // Đảm bảo các trường cần thiết cho giao diện
                $debt->user_name = $debt->user_name ?? '';
                $debt->fee_type = [
                    'name' => $debt->fee_type_name ?? 'Unknown'
                ];

                return $debt;
            });

            // Trả về kết quả với cấu trúc đúng cho Laravel pagination
            return response()->json([
                'data' => $debts->items(),
                'total' => $debts->total(),
                'per_page' => $debts->perPage(),
                'current_page' => $debts->currentPage(),
                'last_page' => $debts->lastPage(),
                'from' => $debts->firstItem(),
                'to' => $debts->lastItem(),
            ]);
        } catch (\Exception $e) {
            Log::error('Error in debt list API: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json(['message' => 'An error occurred while fetching debt data: ' . $e->getMessage()], 500);
        }
    }


    public function create(Request $request)
    {
        return $this->debtRepository->createDebt($request);
    }

    public function cancel(Request $request, $id)
    {
        return $this->debtRepository->cancelDebt($request, $id);
    }

    public function paymentForm($id)
    {
        return $this->debtRepository->paymentForm($id);
    }

    public function payment(Request $request, $id)
    {
        return $this->debtRepository->processPayment($request, $id);
    }

    /**
     * Xuất danh sách công nợ ra file Excel
     */
    public function export(Request $request)
    {
        try {
            // Lấy tham số tìm kiếm từ request
            $keyword = $request->input('keyword');
            $userCode = $request->input('user_code');
            $termId = $request->input('term_id');
            $status = $request->input('status');
            $feeType = $request->input('fee_type');
            $fromDate = $request->input('from_date');
            $toDate = $request->input('to_date');
            $format = $request->input('format', 'excel');

            Log::info('Export request parameters:', $request->all());

            // Query lấy dữ liệu công nợ với các trường mới
            $query = Debt::query()
                ->select(
                    'student_debts.*',
                    'term.term_name',
                    'fee_types.name as fee_type_name',
                    'student_debts.type_fee',
                    'student_debts.ki_thu',
                    'student_debts.version',
                    'student_debts.original_amount',
                    'student_debts.discount_amount',
                    'student_debts.discount_percentage',
                    'student_debts.discount_reason'
                )
                ->leftJoin('term', 'student_debts.term_id', '=', 'term.id')
                ->leftJoin('fee_types', 'student_debts.fee_type_id', '=', 'fee_types.id');

            // Áp dụng các điều kiện lọc
            if ($keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('student_debts.user_code', 'like', "%{$keyword}%")
                        ->orWhere('student_debts.user_login', 'like', "%{$keyword}%");
                });
            }

            if ($userCode) {
                $query->where('student_debts.user_code', 'like', "%{$userCode}%");
            }

            if ($termId) {
                $query->where('student_debts.term_id', $termId);
            }

            if ($status !== null && $status !== '') {
                $query->where('student_debts.status', $status);
            }

            if ($feeType) {
                $query->where('fee_types.name', 'like', "%{$feeType}%");
            }

            if ($fromDate) {
                $query->whereDate('student_debts.created_at', '>=', $fromDate);
            }

            if ($toDate) {
                $query->whereDate('student_debts.created_at', '<=', $toDate);
            }

            $debts = $query->orderBy('student_debts.created_at', 'desc')->get();

            Log::info('Export query result count: ' . $debts->count());

            // Tạo file theo định dạng
            $filename = 'danh_sach_cong_no_' . date('Y-m-d_H-i-s');

            switch ($format) {
                case 'csv':
                    return Excel::download(new DebtExport($debts), $filename . '.csv', \Maatwebsite\Excel\Excel::CSV);
                case 'pdf':
                    return Excel::download(new DebtExport($debts), $filename . '.pdf', \Maatwebsite\Excel\Excel::DOMPDF);
                default:
                    return Excel::download(new DebtExport($debts), $filename . '.xlsx');
            }
        } catch (\Exception $e) {
            Log::error('Lỗi xuất danh sách công nợ: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xuất file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show debt detail page (for web routes)
     */
    public function detail($id)
    {
        try {
            $debt = Debt::findOrFail($id);
            return view('admin_v1.fee.debt.detail', compact('debt'));
        } catch (\Exception $e) {
            Log::error('Error loading debt detail page: ' . $e->getMessage());
            return redirect()->route('admin.debt.list')->with('error', 'Không thể tải thông tin công nợ');
        }
    }

    /**
     * Get debt detail data (for API routes)
     */
    public function getDetailData($id)
    {
        return $this->debtRepository->getDebtDetail($id);
    }

    /**
     * Kiểm tra dữ liệu trong bảng student_debts
     */
    public function checkDebtData()
    {
        try {
            // Lấy tất cả dữ liệu từ bảng student_debts
            $debts = DB::table('student_debts')->get();

            return response()->json([
                'success' => true,
                'count' => count($debts),
                'data' => $debts
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Import debts from Excel file (single step process)
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls|max:10240' // Max 10MB
        ]);

        try {
            $file = $request->file('file');

            Log::info('Starting debt import', [
                'filename' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime' => $file->getMimeType()
            ]);

            // Create import instance and process file
            $import = new DebtBulkImport();
            Excel::import($import, $file);

            // Get results
            $results = $import->getResults();

            // Fix total_rows if it's 0 but we have success_count (chunk processing issue)
            if ($results['total_rows'] == 0 && $results['success_count'] > 0) {
                $results['total_rows'] = $results['success_count'] + $results['error_count'];
                Log::warning('Fixed total_rows count due to chunk processing', [
                    'corrected_total_rows' => $results['total_rows'],
                    'success_count' => $results['success_count'],
                    'error_count' => $results['error_count']
                ]);
            }

            // Determine if import was successful
            $isSuccess = $results['success_count'] > 0 || $results['error_count'] == 0;
            $statusCode = $isSuccess ? 200 : 422;

            // Create appropriate message
            if ($results['success_count'] > 0 && $results['error_count'] == 0) {
                $message = "Import hoàn thành! Tạo thành công {$results['success_count']} công nợ.";
            } elseif ($results['success_count'] > 0 && $results['error_count'] > 0) {
                $message = "Import hoàn thành một phần! Tạo thành công {$results['success_count']} công nợ, {$results['error_count']} lỗi.";
            } else {
                $message = "Import thất bại! Không có công nợ nào được tạo.";
                $isSuccess = false;
                $statusCode = 422;
            }

            // Log final results
            Log::info('Debt import completed', [
                'success' => $isSuccess,
                'total_rows' => $results['total_rows'],
                'success_count' => $results['success_count'],
                'error_count' => $results['error_count'],
                'created_debts_count' => count($results['created_debts'])
            ]);

            // Format errors for better display
            $formattedErrors = [];
            foreach ($results['errors'] as $error) {
                $formattedErrors[] = [
                    'row' => $error['row'],
                    'errors' => $error['errors'] ?? [$error['message']],
                    'data' => $error['data']
                ];
            }

            return response()->json([
                'success' => $isSuccess,
                'message' => $message,
                'total_rows' => $results['total_rows'],
                'success_count' => $results['success_count'],
                'error_count' => $results['error_count'],
                'errors' => $formattedErrors,
                'created_debts' => $results['created_debts']
            ], $statusCode);
        } catch (\Exception $e) {
            Log::error('Debt import failed: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Import thất bại: ' . $e->getMessage(),
                'details' => env('APP_DEBUG', false) ? $e->getTraceAsString() : null
            ], 500);
        }
    }



    /**
     * Download Excel template for bulk import
     */
    public function downloadTemplate()
    {
        try {
            Log::info('Generating debt import template');
            return Excel::download(new SimpleExcelTemplate(), 'mau_import_cong_no.xlsx');
        } catch (\Exception $e) {
            Log::error('Error generating template: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tạo file mẫu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download sample Excel file with real data for testing
     */
    public function downloadSample()
    {
        try {
            Log::info('Generating debt import sample with real data');
            return Excel::download(new DebtImportSampleExport(), 'vi_du_import_cong_no_du_lieu_that.xlsx');
        } catch (\Exception $e) {
            Log::error('Error generating sample: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tạo file ví dụ: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get reference data for import (terms and fee types)
     */
    public function getReferenceData()
    {
        try {
            $terms = Term::select('id', 'term_name')
                ->orderBy('ordering', 'desc')
                ->limit(10)
                ->get();

            $feeTypes = \App\Models\Fee\FeeType::select('id', 'name')
                ->where('is_active', true)
                ->orderBy('display_order')
                ->get();

            return response()->json([
                'success' => true,
                'terms' => $terms,
                'fee_types' => $feeTypes
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting reference data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy dữ liệu tham chiếu'
            ], 500);
        }
    }
}
