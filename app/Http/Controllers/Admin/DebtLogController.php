<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Fee\DebtActivityLog;
use App\Models\Fee\WalletActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DebtLogController extends Controller
{
    /**
     * L<PERSON>y danh sách log hoạt động của debt
     */
    public function getDebtLogs(Request $request)
    {
        try {
            $query = DebtActivityLog::with('debt');

            // Filter by debt_id
            if ($request->filled('debt_id')) {
                $query->where('debt_id', $request->debt_id);
            }

            // Filter by user_code
            if ($request->filled('user_code')) {
                $query->where('user_code', $request->user_code);
            }

            // Filter by action
            if ($request->filled('action')) {
                $query->where('action', $request->action);
            }

            // Filter by actor
            if ($request->filled('actor')) {
                $query->where('actor', 'like', "%{$request->actor}%");
            }

            // Filter by date range
            if ($request->filled('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }
            if ($request->filled('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            // Pagination
            $perPage = $request->input('per_page', 20);
            $logs = $query->orderBy('created_at', 'desc')
                         ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $logs->items(),
                'total' => $logs->total(),
                'per_page' => $logs->perPage(),
                'current_page' => $logs->currentPage(),
                'last_page' => $logs->lastPage()
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching debt logs: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải log hoạt động: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy danh sách log hoạt động của wallet
     */
    public function getWalletLogs(Request $request)
    {
        try {
            $query = WalletActivityLog::with('wallet');

            // Filter by wallet_id
            if ($request->filled('wallet_id')) {
                $query->where('wallet_id', $request->wallet_id);
            }

            // Filter by user_code
            if ($request->filled('user_code')) {
                $query->where('user_code', $request->user_code);
            }

            // Filter by action
            if ($request->filled('action')) {
                $query->where('action', $request->action);
            }

            // Filter by actor
            if ($request->filled('actor')) {
                $query->where('actor', 'like', "%{$request->actor}%");
            }

            // Filter by date range
            if ($request->filled('from_date')) {
                $query->whereDate('created_at', '>=', $request->from_date);
            }
            if ($request->filled('to_date')) {
                $query->whereDate('created_at', '<=', $request->to_date);
            }

            // Filter by money actions only
            if ($request->boolean('money_actions_only')) {
                $query->moneyActions();
            }

            // Pagination
            $perPage = $request->input('per_page', 20);
            $logs = $query->orderBy('created_at', 'desc')
                         ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $logs->items(),
                'total' => $logs->total(),
                'per_page' => $logs->perPage(),
                'current_page' => $logs->currentPage(),
                'last_page' => $logs->lastPage()
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching wallet logs: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải log hoạt động: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy log chi tiết của một debt cụ thể
     */
    public function getDebtDetailLogs($debtId)
    {
        try {
            $logs = DebtActivityLog::where('debt_id', $debtId)
                                  ->orderBy('created_at', 'desc')
                                  ->get();

            return response()->json([
                'success' => true,
                'data' => $logs
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching debt detail logs: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải log chi tiết: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy log chi tiết của một wallet cụ thể
     */
    public function getWalletDetailLogs($walletId)
    {
        try {
            $logs = WalletActivityLog::where('wallet_id', $walletId)
                                   ->orderBy('created_at', 'desc')
                                   ->get();

            return response()->json([
                'success' => true,
                'data' => $logs
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching wallet detail logs: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải log chi tiết: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy thống kê log hoạt động
     */
    public function getLogStatistics(Request $request)
    {
        try {
            $fromDate = $request->input('from_date', now()->subDays(30));
            $toDate = $request->input('to_date', now());

            // Debt statistics
            $debtStats = [
                'total_actions' => DebtActivityLog::dateRange($fromDate, $toDate)->count(),
                'by_action' => DebtActivityLog::dateRange($fromDate, $toDate)
                    ->selectRaw('action, COUNT(*) as count')
                    ->groupBy('action')
                    ->pluck('count', 'action'),
                'by_actor_type' => DebtActivityLog::dateRange($fromDate, $toDate)
                    ->selectRaw('actor_type, COUNT(*) as count')
                    ->groupBy('actor_type')
                    ->pluck('count', 'actor_type')
            ];

            // Wallet statistics
            $walletStats = [
                'total_actions' => WalletActivityLog::dateRange($fromDate, $toDate)->count(),
                'by_action' => WalletActivityLog::dateRange($fromDate, $toDate)
                    ->selectRaw('action, COUNT(*) as count')
                    ->groupBy('action')
                    ->pluck('count', 'action'),
                'by_actor_type' => WalletActivityLog::dateRange($fromDate, $toDate)
                    ->selectRaw('actor_type, COUNT(*) as count')
                    ->groupBy('actor_type')
                    ->pluck('count', 'actor_type'),
                'total_amount_involved' => WalletActivityLog::dateRange($fromDate, $toDate)
                    ->whereNotNull('amount_involved')
                    ->sum('amount_involved')
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'debt_statistics' => $debtStats,
                    'wallet_statistics' => $walletStats,
                    'date_range' => [
                        'from' => $fromDate,
                        'to' => $toDate
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching log statistics: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải thống kê: ' . $e->getMessage()
            ], 500);
        }
    }
}
