<?php

namespace App\Console\Commands\SMS;

use App\Models\Fu\Block;
use App\Models\Fu\Term;
use App\Models\Sna\Sms\SnaSmsWarning;
use App\Models\T7\CourseResult;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateDataWarningSms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-data-warning-sms';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Kéo dữ liệu từ Tình hình đi học cập nhật vào Tool quản lý tin nhắn cảnh báo.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Pull data from student attendance and update it into warning data.
     *
     * @return void
     */
    public function handle()
    {
        
        Log::channel('sms')->info('---------- Update Student Warning on----------');
        $now = Carbon::now();
        $dateTime = $now->toDateTimeString();
        $currentTerm = Term::whereRaw('startday <= CURRENT_DATE')->whereRaw('endday >= CURRENT_DATE')->first();
        $currentBlock = Block::whereRaw('start_day <= CURRENT_DATE')->whereRaw('end_day >= CURRENT_DATE')->first();
        try {
            $data_update = $this->pullStudentWarning($currentTerm, $currentBlock, $dateTime);
            Log::channel('sms')->info('---------- Array update on = ' . count($data_update) . ' ----------');
            DB::beginTransaction();
            foreach (array_chunk($data_update, 500) as $data)  
            {
                SnaSmsWarning::upsert($data,
                ['student_login', 'group_id', 'attendance_absent'], 
                ['attendance_absent', 'student_code', 'student_name', 'block_id', 'end_date', 'group_name', 'subject_code', 'term_id', 'last_status', 'status',
                'done_activity','total_session', 'max_absent_percent', 'absent_percent', 'last_activity_id', 'date_modified']);
            }
            DB::commit();
            Log::channel('sms')->info('----------Done Update Student Warning on----------');
        } catch (\Throwable $th) {
            Log::error("--------- update-data-warning-sms ---------");
            Log::error($th);
            Log::error("--------- update-data-warning-sms ---------");
            DB::rollBack();
        }
    }
    /**
     * Pull data from student attendance.
     * @param  number $currentTerm
     * @param  number $currentBlock
     * @param  string $dateTime
     * @return array
     */
    public function pullStudentWarning($currentTerm, $currentBlock, $dateTime)
    {
        try {
            $list_warning = CourseResult::join('user', 'user.user_login', '=', 't7_course_result.student_login')
            ->select([
                'user.user_login as student_login', 
                'user.user_code as student_code', 
                DB::raw("CONCAT(TRIM(user.user_surname),' ', TRIM(user.user_middlename),' ',TRIM(user.user_givenname)) AS student_name"), 
                't7_course_result.groupid as group_id', 
                't7_course_result.pgroup_name as group_name', 
                't7_course_result.psubject_code as subject_code', 
                't7_course_result.term_id as term_id' ,
                't7_course_result.attendance as status',
                't7_course_result.attendance as last_status',
                't7_course_result.attendance_absent',
                't7_course_result.done_activity',
                't7_course_result.total_session as total_session', 
                DB::raw('(100-t7_course_result.attendance_cutoff) as max_absent_percent'), 
                DB::raw('(t7_course_result.attendance_absent/t7_course_result.total_session*100) AS absent_percent'), 
                't7_course_result.end_date',
                // DB::raw('MAX(activity.id) as last_activity_id')
                DB::raw('(SELECT activity.id FROM activity WHERE t7_course_result.groupid = activity.groupid AND activity.done = 1
                ORDER BY activity.day DESC LIMIT 1) as last_activity_id'),
                DB::raw('CURRENT_TIMESTAMP as date_modified')
            ])
            ->whereNotIn('user.study_status', [4, 5, 6, 9])
            ->where('user.user_level', '=', '3')->where('t7_course_result.total_session', '!=', '0')
            ->where('t7_course_result.total_session', '!=', '0')->where('t7_course_result.done_activity', '!=', '0')
            ->where('t7_course_result.term_id', '=', $currentTerm['id'])
            ->where('t7_course_result.attendance_absent', '>', '0')
            ->whereRaw('t7_course_result.end_date > CURRENT_DATE')
            ->whereRaw('(((t7_course_result.attendance_absent + 3)/t7_course_result.total_session*100) - (100 - t7_course_result.attendance_cutoff)) > 0')
            ->get()->toArray();
            for($i = 0; $i < count($list_warning); $i++){
                $list_warning[$i]['date_created'] = $dateTime;
                $list_warning[$i]['block_id'] = $currentBlock['id'];
            }
            return $list_warning;
        } catch (\Throwable $th) {
            Log::error("--------- start err pullStudentWarning ---------");
            Log::error($th);
            Log::error("--------- end err pullStudentWarning ---------");
            return [];
        }
    }

}
