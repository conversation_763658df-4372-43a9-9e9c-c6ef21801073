<?php

namespace App\Console\Commands\Sms;
use App\Repositories\Admin\SmsRepository;
use Illuminate\Support\Facades\Log;
use App\Models\Sms\StudentSmsList;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SendSms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:send-sms';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gửi tin nhắn hẹn giờ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->check();

    }
    public function check()
    {
        $now = Carbon::now();
        $listSms = StudentSmsList::select('id', 'send_time')->where('status_list', 1)->where('send_time','!=', null)->get();
        foreach($listSms as $list){
            if($now->diffInMinutes($list->send_time, true) <= 10){
                Log::channel('sms')->info(["Gửi tin nhắn hẹn giờ:"=>["list_id" => $list->id]]);
                $smsRepository = new SmsRepository;
                $smsRepository->sendSmsTimer($list->id);
            }
        }
    }
}
