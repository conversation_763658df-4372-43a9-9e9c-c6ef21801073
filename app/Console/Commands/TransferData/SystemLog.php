<?php

namespace App\Console\Commands\TransferData;

use App\Models\Fu\User;
use Illuminate\Console\Command;
use App\Models\SystemLog as SystemLogDB;
use Illuminate\Support\Facades\DB;

class SystemLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transfer:system_log';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Lưu trữ systemlog (TNG)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        
        $study_status = [8,4];

        try {
            $countUser = User::where('user_level', 3)->whereIn('study_status', $study_status)->count();
            $num = ceil($countUser / 1000);
            $this->info("Thực hiện $num lần");
            $per = 0;
            for ($i = 0;$i < $num * 1000;$i+=1000) {
                $per++;
                self::transferSystemLog($i, 1000);
                $this->info("Hoàn tất lần $per, skip $i limit 1000");
            }
        } catch (\Exception $e) {
            $this->error('Lỗi english:update');
        }
    }

    public static function transferSystemLog($skip, $limit = 2000)
    {
        $study_status = [8,4];
        $users = User::select('id','user_code','user_login','study_status')->where('user_level', 3)->whereIn('study_status', $study_status)->skip($skip)->limit($limit)->get();
        foreach ($users as $user) {
            $system_log = SystemLogDB::where('relation_login', $user->user_login)->get();
            foreach ($system_log as $item) {
                DB::table('system_log_backup')->insert($item->toArray());
                $item->delete();
            }

        }
    }
}
