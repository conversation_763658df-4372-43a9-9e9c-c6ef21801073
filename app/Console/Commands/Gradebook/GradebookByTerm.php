<?php

namespace App\Console\Commands\Gradebook;

use App\Exports\GradeBook\GradeBookExport;
use App\Jobs\ExportGradebook;
use App\Models\Fu\Block;
use App\Repositories\Admin\EducateRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class GradebookByTerm extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gradebook-export';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Xuất file excel bảng điểm theo học kỳ và block';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public $timeout = 7200;
    
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $now = Carbon::now();
            
            $current_block = Block::whereDate('start_day', '<=', $now)
                ->whereDate('end_day', '>=', $now)
                ->firstOrFail();
            $start_block_date = Carbon::parse($current_block->start_day);
            $diff = $start_block_date->diffInDays($now);
            if ($diff >= 10) {
                $data = (object)[
                    'term_id' => $current_block->term_id,
                    'block_id' => $current_block->id,
                    'group_ids' => [],
                    'subject_codes' => [],
                ];
                dispatch(new ExportGradebook($data, null));
            }
        } catch (\Throwable $th) {
            error_log($th);
            Log::channel('schedule')->error("-----------------------start err GradebookByTerm-------------------");
            Log::channel('schedule')->error($th);
            Log::channel('schedule')->error("-----------------------end err GradebookByTerm-------------------");
        }
        
        return 0;
    }
}
