<?php

namespace App\Console\Commands\Fee;

use App\Http\Lib;
use App\Models\Fee\Fee;
use App\Models\Fee\FeeDetail;
use App\Models\Fee\FeeLog;
use App\Models\Fee\Transaction;
use App\Models\EnglishDetail;
use App\Models\EnglishDetailLevel;
use App\Models\Fee\FeeMailLog;
use App\Models\Fu\Term;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class RefundNotStudy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fee:refund_not_study {term_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Hồi phí cho sinh viên không học đi THO, TN1, TN2';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // status để check 
        $status = [
            1 => "DH (Đang học)",
            2 => "HL (Học lại)",
            3 => "TN (Tạm nghỉ)",
            4 => "BH (Bỏ học)",
            5 => "CXL (Chờ xếp lớp)",
            6 => "CTN (Chờ tốt nghiệp)",
            7 => "TNG (Đã tốt nghiệp)",
        ];

        // Trạng thái hồi TN1, TN2, TN21
        
        $termId = $this->argument('term_id');
        Log::channel('fee-semester')->info("================================== Refund sinh viên không học đi " . now()->format(' Y-m-d h:i:s ') . "==================================");
        DB::beginTransaction();
        try {
            $countUser = Fee::leftJoin('fee_mails', 'fees.user_login', '=', 'fee_mails.user_login')
                ->leftJoin('user', 'user.user_login', '=', 'fee_mails.user_login')
                ->where('user.study_status', '!=', 1)
                ->where('fee_mails.term_id', $termId)
                ->count();

            $num = ceil($countUser / 1000);
            $this->info("Thực hiện $num lần");
            $per = 0;
            for ($i = 0;$i < $num * 1000;$i+=1000) {
                $per++;
                self::refund($termId, $i, 1000);
                $this->info("Hoàn tất lần $per, skip $i limit 1000");
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            $this->error('Lỗi english:update');
        }
    }

    public static function refund($termId, $skip, $limit = 2000)
    {
        $term = Lib::getTermDetails();
        $users = Fee::select('fees.*', DB::raw('fee_mails.id as fee_mail_id'), DB::raw('fee_mails.brand_code as fee_mail_brand_code'))
            ->leftJoin('fee_mails', 'fees.user_login', '=', 'fee_mails.user_login')
            ->leftJoin('user', 'user.user_login', '=', 'fee_mails.user_login')
            ->where('user.study_status', '!=', 1)
            ->where('fee_mails.term_id', $termId)
            ->skip($skip)
            ->limit($limit)
            ->get();
        
        foreach ($users as $user) {
            // echo "$user->user_code - $user->brand_code\n";
            // Log::channel('fee-semester')->info(("[infor Refund] User:\t $user->user_code - $user->brand_code"));
            $refund = 0;
            $ki_thu_phi = $user->ki_thu + 1;
            $logs = FeeLog::where('fee_id', $user->id)
            ->where('brand_code', $user->brand_code)
            ->where(function ($q) use ($user) {
                $q->where('brand_code', $user->brand_code);
                $q->orWhere('brand_code', $user->fee_mail_brand_code);
            })
            // ->where('brand_code', '!=', $user->brand_code) // 2 Phần cmt này dùng nếu bên đào tạo up lại ngành sau khi trừ phí
            // ->where('created_at', '>=', '2022-04-26 00:13:48') // Thời gian bắt đầu trừ phí 
            ->where('ki_thu', $ki_thu_phi)
            ->where('term_id', $termId)
            ->get();
            
            // ======= Hồi Học phí BT =======
            foreach ($logs as $log) {
                echo "$log->amount\n";
                // Log::channel('fee-semester')->info(("[infor Refund] Amount:\t " . $log->amount));
                $refund = $refund + $log->amount;
                $log->delete();
            }
            
            if ($refund > 0) {
                $user->study_wallet = $user->study_wallet + $refund;
                $user->version = 0;
                echo "$user->user_code - $user->brand_code\n";
                echo "$refund\n";
                Transaction::create([
                    'user_code' => $user->user_code,
                    'user_login' => $user->user_login,
                    'type' => 'RF',
                    'type_extension' => 'Refund',
                    'invoice_id' => 0,
                    'amount' => $refund,
                    'execute' => 1,
                    'in_out' => 1,
                    'note' => "Refund sinh viên không học đi",
                    'invoice_date_create' => now(),
                    'term_name' => $term['term_name'],
                    'created_by' => 'system',
                ]);

                FeeMailLog::create([
                    'fee_mail_id' => $user->fee_mail_id,
                    'auth' => 'system',
                    'user_login' => $user->user_login,
                    'action' => FeeMailLog::ACTION_REFUND,
                    'description' => "Hồi phí sinh viên không học đi",
                    'data' => json_encode([
                        'amount' => ($refund)
                    ]),
                    'id_action' => request()->ip()
                ]);
                
                Log::channel('fee-semester')->info("[infor Refund] User:\t $user->user_code - $user->brand_code: Amount\t$refund");
            }
            
            FeeDetail::where('fee_id', $user->id)->where('ki_thu', $ki_thu_phi)->whereIn('type_fee', [3,4])->update([
                'status' => 0,
            ]);
            $user->save();
        }
    }
}
