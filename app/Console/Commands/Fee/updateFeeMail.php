<?php

namespace App\Console\Commands\Fee;

use App\Http\Controllers\Admin\SystemController;
use App\Models\EnglishDetailLevel;
use App\Models\EnglishDetail;
use App\Models\Fee\Fee;
use App\Models\Fee\Plan;
use App\Models\Fee\FeeDetail;
use App\Models\Fee\FeeLog;
use App\Models\Fee\FeeMail;
use App\Models\Fee\FeeMailLog;
use App\Models\Fee\Transaction;
use App\Models\Fu\Term;
use App\Models\T7\CourseResult;
use App\Models\TranferT7Course;
use App\Models\Fu\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;


class updateFeeMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fee:update_fee_mail {term_id} {auth=system}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Xử lý số dư học phí';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */


    public function handle()
    {
        ini_set('memory_limit', '-1');
        // 
        
        $termId = $this->argument('term_id');
        $auth = $this->argument('auth');
        // $termId = 50;
        $listMajorTravel = [
            'HDDL',
            'HDDL-T',
            'HDDL01',
            'QTNH-T',
            'QTNH01',
            'QTNH',
            'QTKS',
            'QTKS01',
            'QTKS-T',
        ];
        // danh sách Trạng thái tiếng anh
        $listStatusEnglish = [
            -4 => 'Thi lại',
            -3 => 'Chưa đạt',
            -2 => 'Đang học',
            -1 => 'Trượt điểm danh',
            0 => 'Chưa học',
            1 => 'Đạt',
            2 => 'Miễn Giảm'
        ];

        // Lấy danh sách mail phí
        $term = Term::find($termId);
        $dataErr = null;
        DB::beginTransaction();
        try {
            // xử lý tiếng anh Du lịch nhà nhàng khách sạn
            $totalFeeMailTravel = FeeMail::where('term_id', $termId)
            ->whereIn('brand_code', $listMajorTravel)
            // ->where('tieng_anh', '>', 0)
            ->get();

            foreach ($totalFeeMailTravel as $mail) {
                $dataErr = $mail;
                $fee = Fee::where('user_code', $mail->user_code)
                ->first();
    
                // Kiểm tra trạng thái tiếng anh
                $english = EnglishDetail::select('id')
                ->where('user_code', $mail->user_code)
                ->first();
                if ($english) {
                    $oldFee = $mail->tieng_anh;
                    $lastEnglish = EnglishDetailLevel::where('level', $mail->english_level)
                    ->where('english_id', $english->id)
                    ->where('term_id', '<', $termId)
                    // ->orderBy('id', 'DESC')
                    ->orderBy('create_time','desc') 
                    ->first();
    
                    // Học lại
                    if (!$lastEnglish) continue;
                    if ($mail->tieng_anh > 0 && in_array($lastEnglish->status, [1, 2])) {
                        // Kiểm tra xem đã trừ phí chưa? 
                        $feeEngCheck = Transaction::where('user_login', $mail->user_login)
                        ->where('note', 'Dự thu phí tiếng anh kỳ ' . $term->term_name)
                        ->count();
                        if ($feeEngCheck > 0) {
                            // lấy ví
                            Transaction::create([
                                'user_code' => $fee->user_code,
                                'user_login' => $fee->user_login,
                                'type' => 'HP',
                                'type_extension' => 'Tiếng anh dự thu|Lv' . $mail->english_level,
                                'invoice_id' => 0,
                                'amount' => $mail->tieng_anh,
                                'execute' => 1,
                                'in_out' => 1,
                                'note' => "Hồi phí dự thu phí tiếng anh kỳ $term->term_name Do hoàn thành tiếng anh",
                                'invoice_date_create' => now(),
                                'term_name' => $term->term_name,
                                'created_by' => 'system-dev',
                            ]);
    
                            $fee->study_wallet = $fee->study_wallet + $mail->tieng_anh;
                            // $mail->amount = $mail->amount - $mail->tieng_anh;
                            $mail->english_status = 0;
                            $mail->tieng_anh = 0;
                            $mail->save();
                            $fee->save();
                            Log::channel('fee-semester')->info("Hồi phí dự thu tiếng anh cho sinh viên $fee->user_code ($mail->brand_code) Do đã qua tiếng anh LV$mail->english_level Kỳ $term->term_name ($lastEnglish->id)");
                        } else {
                            Log::channel('fee-semester')->info("Cập nhập lại phí tiếng anh về 0 cho sinh viên $fee->user_code do sinh viên thi lại đã qua");
                            $mail->tieng_anh = 0;
                            $mail->save();
                        }
                    }
                }
            }


            // xử lý tiếng anh của sinh viên khóa 19.x
            $totalFeeMailNewCurriculumn = FeeMail::select('fee_mails.*')
            ->leftJoin('user', 'user.user_login', '=', 'fee_mails.user_login')
            ->leftJoin('curriculum', 'curriculum.id', 'user.curriculum_id')
            ->where('term_id', $termId)
            ->where('type_student', FeeMail::TYPE_STUDENT_CHANGE_BRAND)
            ->where('curriculum.khoa', '>=', 19)
            ->whereNotIn('fee_mails.brand_code', $listMajorTravel)
            ->get();

            foreach ($totalFeeMailNewCurriculumn as $mail) {
                $dataErr = $mail;
                $fee = Fee::where('user_code', $mail->user_code)
                ->first();
    
                // Kiểm tra trạng thái tiếng anh
                $english = EnglishDetail::select('id')
                ->where('user_code', $mail->user_code)
                ->first();
                if ($english) {
                    $oldFee = $mail->tieng_anh;
                    $lastEnglish = EnglishDetailLevel::where('level', $mail->english_level)
                    ->where('english_id', $english->id)
                    ->where('term_id', '<', $termId)
                    // ->orderBy('id', 'DESC')
                    ->orderBy('create_time','desc') 
                    ->first();
    
                    // Học lại
                    if (!$lastEnglish) continue;
                    if ($mail->tieng_anh > 0 && in_array($lastEnglish->status, [1, 2])) {
                        // Kiểm tra xem đã trừ phí chưa? 
                        $feeEngCheck = Transaction::where('user_login', $mail->user_login)
                        ->where('note', 'Dự thu phí tiếng anh kỳ ' . $term->term_name)
                        ->count();

                        if ($feeEngCheck > 0) {
                            // lấy ví
                            Transaction::create([
                                'user_code' => $fee->user_code,
                                'user_login' => $fee->user_login,
                                'type' => 'HP',
                                'type_extension' => 'Tiếng anh dự thu|Lv' . $mail->english_level,
                                'invoice_id' => 0,
                                'amount' => $mail->tieng_anh,
                                'execute' => 1,
                                'in_out' => 1,
                                'note' => "Hồi phí dự thu phí tiếng anh kỳ $term->term_name do chuyển sang khung 19.x",
                                'invoice_date_create' => now(),
                                'term_name' => $term->term_name,
                                'created_by' => 'system-dev',
                            ]);
    
                            $fee->study_wallet = $fee->study_wallet + $mail->tieng_anh;
                            // $mail->amount = $mail->amount - $mail->tieng_anh;
                            $mail->english_status = 0;
                            $mail->tieng_anh = 0;
                            $mail->save();
                            $fee->save();
                            Log::channel('fee-semester')->info("Hồi phí dự thu tiếng anh cho sinh viên $fee->user_code ($mail->brand_code) Do chuyển về khung 19.x tiếng anh LV$mail->english_level Kỳ $term->term_name ($lastEnglish->id)");
                        } else {
                            Log::channel('fee-semester')->info("Cập nhập lại phí tiếng anh về 0 cho sinh viên $fee->user_code do sinh viên chuyển về khung 19.x");
                            $mail->tieng_anh = 0;
                            $mail->save();
                        }
                    }
                }
            }

            /* ==================== Kiểm tra danh sách sinh viên khác khung ==================== */
            // Lấy danh sách sinh viên quét được 
            $listDiffBrand = DB::select("SELECT 
                    fee_mails.id as mail_id,
                    fee_mails.user_login,
                    fee_mails.user_code,
                    fee_mails.status_fee,
                    fee_mails.term_id,
                    fee_mails.ki_thu,
                    fee_mails.brand_code as brand_code_mail,
                    tbl_brand_code.brand_code as brand_code_current
                FROM fee_mails 
                LEFT JOIN fees ON fees.user_login = fee_mails.user_login
                LEFT JOIN (
                    SELECT 
                        user.user_code,
                        user.user_login,
                        curriculum.brand_code
                    FROM user
                    JOIN curriculum ON curriculum.id = user.curriculum_id
                    WHERE user.user_login IN (
                        SELECT 
                            fee_mails.user_login
                        FROM fee_mails 
                        WHERE term_id = ?
                    )
                ) tbl_brand_code ON tbl_brand_code.user_login = fee_mails.user_login
                WHERE term_id = ?
                AND is_locked = 0
                AND tbl_brand_code.brand_code != fee_mails.brand_code", [$termId, $termId]);

            // Duyệt sinh viên
            $listUserChangeMajor = array_map(function ($a) {
                return $a->user_code;
            }, $listDiffBrand);

            $listUserUpdate = User::select([
                'id',
                'user_code',
                'user_login',
                'curriculum_id',
                'study_status',
                'kithu',
            ])
            // ->whereIn('study_status', [1,3,10])
            ->where('user_level', 3)
            ->whereIn('user_code', $listUserChangeMajor)
            ->get();

            foreach ($listDiffBrand as $key => $mail) {
                // Nếu chưa trừ
                // Đổi lại trạng thái và ngành phí
                $user = $listUserUpdate->where('user_code', $mail->user_code)->first();
                if ($mail->status_fee == 0) {
                    // Kiểm tra và thêm mới log 
                    FeeMail::where('id', $mail->mail_id)
                    ->update([
                        'status_fee' => 0,
                        'brand_code' => $mail->brand_code_current,
                        'type_student' => FeeMail::TYPE_STUDENT_CHANGE_BRAND
                    ]);
                    
                    FeeMailLog::create([
                        'fee_mail_id' => $mail->mail_id,
                        'auth' => $auth,
                        'user_login' => $mail->user_login,
                        'action' => FeeMailLog::ACTION_UPDATE,
                        'description' => "Chuyển ngành từ $mail->brand_code_mail sang Ngành $mail->brand_code_current",
                        'data' => json_encode([
                            'old_brand' => $mail->brand_code_mail,
                            'new_brand' => $mail->brand_code_current,
                        ]),
                        'id_action' => request()->ip()
                    ]);
                    
                    SystemController::createOrUpdateFeeRow($user);
                } else {
                    // === Nếu đã trừ === 
                    $fee = Fee::select([
                        'id',
                        'user_code',
                        'user_login',
                        'study_wallet'
                    ])
                    ->where('user_login', $mail->user_login)
                    ->first();

                    $checkMinusFee = FeeLog::where('fee_id', $fee->id)
                        ->where('term_id', $mail->term_id)
                        ->where('brand_code', $mail->brand_code_current)
                        ->get();
                    // Kiểm tra trừ đúng ngành hay không
                    if (count($checkMinusFee) > 0) {
                        FeeMail::where('id', $mail->mail_id)
                            ->update([
                                'brand_code' => $mail->brand_code_current,
                                'type_student' => FeeMail::TYPE_STUDENT_CHANGE_BRAND
                            ]);
                        
                        FeeMailLog::create([
                            'fee_mail_id' => $mail->mail_id,
                            'auth' => $auth,
                            'user_login' => $fee->user_login,
                            'action' => FeeMailLog::ACTION_UPDATE,
                            'description' => "Chuyển ngành từ $mail->brand_code_mail sang Ngành $mail->brand_code_current",
                            'data' => json_encode([
                                'old_brand' => $mail->brand_code_mail,
                                'new_brand' => $mail->brand_code_current,
                            ]),
                            'id_action' => request()->ip()
                        ]);

                        SystemController::createOrUpdateFeeRow($user);
                    } else {
                        // ==== Nếu trừ sai ngành ==== 
                        $checkMinusFeeDiffBrand = FeeLog::where('fee_id', $fee->id)
                            ->where('term_id', $mail->term_id)
                            ->where('brand_code', '!=', $mail->brand_code_current)
                            ->get();
    
                        // kiểm tra xem đã trừ chưa
                        if ($checkMinusFeeDiffBrand) {
                            // Hồi phí chuyển ngành
                            $refund = 0;
                            foreach ($checkMinusFeeDiffBrand as $log) {
                                Log::channel('fee-semester')->info(("[user: $fee->user_code] Refund chuyển ngành:\t " . $log->amount));
                                $refund = $refund + $log->amount;
                                $log->delete();
                            }

                            $fee->study_wallet = $fee->study_wallet + $refund;
                            $fee->save();
                            // Thêm bản ghi trừ phí
                            Transaction::create([
                                'user_code' => $fee->user_code,
                                'user_login' => $fee->user_login,
                                'type' => 'RF',
                                'type_extension' => 'Refund',
                                'invoice_id' => 0,
                                'amount' => $refund,
                                'execute' => 1,
                                'in_out' => 1,
                                'note' => "Refund sinh viên chuyển ngành sau trừ phí",
                                'invoice_date_create' => now(),
                                'term_name' => $term['term_name'],
                                'created_by' => 'system',
                            ]);

                            // + Đổi lại dữ liệu và lưu log
                            // Thêm log hồi phí 
                            FeeMailLog::create([
                                'fee_mail_id' => $mail->mail_id,
                                'auth' => $auth,
                                'user_login' => $fee->user_login,
                                'action' => FeeMailLog::ACTION_REFUND,
                                'description' => 'Refund sinh viên chuyển ngành',
                                'data' => json_encode([
                                    'refund' => $refund,
                                    'old_brand' => $mail->brand_code_mail,
                                    'new_brand' => $mail->brand_code_current,
                                ]),
                                'id_action' => request()->ip()
                            ]);

                            // Cập nhập lại trạng thái phí
                            FeeMail::where('id', $mail->mail_id)
                                ->update([
                                    'ki_thu' => ($user->ki_thu + 1),
                                    'status_fee' => 0,
                                    'brand_code' => $mail->brand_code_current,
                                    'type_student' => FeeMail::TYPE_STUDENT_CHANGE_BRAND
                                ]);

                            // Thêm log mail
                            FeeMailLog::create([
                                'fee_mail_id' => $mail->mail_id,
                                'auth' => $auth,
                                'user_login' => $fee->user_login,
                                'action' => FeeMailLog::ACTION_UPDATE,
                                'description' => "Chuyển ngành từ $mail->brand_code_mail sang Ngành $mail->brand_code_current",
                                'data' => json_encode([
                                    'old_brand' => $mail->brand_code_mail,
                                    'new_brand' => $mail->brand_code_current,
                                ]),
                                'id_action' => request()->ip()
                            ]);

                            
                            FeeDetail::where('fee_id', $fee->id)
                                ->where('ki_thu', $mail->ki_thu)
                                ->update([
                                    'status' => 0,
                                ]);

                            // Cập nhập lại phí trước đã
                            SystemController::createOrUpdateFeeRow($user);

                            // Chạy lệnh trừ phí
                            Artisan::call("fee:sync_new_by_user", [
                                'user_code' => $fee->user_code,
                                'term_id' => $term->id
                            ]);
                            
                        }
                    }
                }
            }


            /* ==================== Kiểm tra danh sách sinh THO và TNG ==================== */
            // Lấy danh sách sinh viên quét được
            $listDifStudyStatus = DB::select("SELECT 
                fee_mails.id as mail_id,
                fee_mails.user_login,
                fee_mails.user_code,
                user.study_status as study_status_user,
                fee_mails.study_status as study_status_fee_mails,
                fee_mails.status_fee,
                fee_mails.type_student,
                fee_mails.term_id,
                fee_mails.ki_thu,
                fee_mails.brand_code as brand_code_mail,
                tbl_brand_code.brand_code as brand_code_current
            FROM fee_mails 
            LEFT JOIN fees ON fees.user_login = fee_mails.user_login
            LEFT JOIN user ON user.user_login = fee_mails.user_login
            LEFT JOIN (
                SELECT 
                    user.user_code,
                    user.user_login,
                    user.study_status,
                    curriculum.brand_code
                FROM user
                JOIN curriculum ON curriculum.id = user.curriculum_id
                WHERE user.user_login IN (
                    SELECT 
                        fee_mails.user_login
                    FROM fee_mails 
                    WHERE term_id = ?
                )
            ) tbl_brand_code ON tbl_brand_code.user_login = fee_mails.user_login
            WHERE term_id = ?
            AND fee_mails.is_locked = 0
            AND fee_mails.type_student NOT IN (1,2)
            AND tbl_brand_code.study_status != fee_mails.study_status", [$termId, $termId]);

            $listDetailTypeStatus = FeeMail::LIST_TYPE_STUDENT;
            foreach ($listDifStudyStatus as $key => $value) {
                $statusUpdate = null;
                // user.study_status as study_status_user,
                // fee_mails.study_status as study_status_fee_mails,
                switch ($value->study_status_user) {
                    case 1:
                        if ($value->study_status_fee_mails == 4) {
                            $statusUpdate = FeeMail::TYPE_STUDENT_NHTL;
                        } else {
                            $statusUpdate = FeeMail::TYPE_STUDENT_HDI;
                        }
                        break;
                    case 3:
                        $statusUpdate = FeeMail::TYPE_STUDENT_BL;
                        break;
                    case 4:
                        $statusUpdate = FeeMail::TYPE_STUDENT_THO;
                        break;
                    case 5:
                        $statusUpdate = FeeMail::TYPE_STUDENT_CCS;
                        break;
                    case 8:
                        $statusUpdate = FeeMail::TYPE_STUDENT_TNG;
                        break;
                    case 12:
                        $statusUpdate = FeeMail::TYPE_STUDENT_BB1;
                        break;
                    case 13:
                        $statusUpdate = FeeMail::TYPE_STUDENT_BB2;
                        break;
                }

                if ($statusUpdate == null) {
                    if (in_array($value->study_status_user, [10,16,17,11])) {
                        $statusUpdate = FeeMail::TYPE_STUDENT_HL;
                    }
                }

                if ($statusUpdate != null && $statusUpdate != $value->type_student) {
                    // Cập nhập lại mail
                    FeeMail::where('id', $value->mail_id)
                    ->update([
                        'type_student' => $statusUpdate
                    ]);
                    
                    FeeMailLog::create([
                        'fee_mail_id' => $value->mail_id,
                        'auth' => $auth,
                        'user_login' => $value->user_login,
                        'action' => FeeMailLog::ACTION_UPDATE,
                        'description' => "Cập nhập trạng thái sinh viên từ " . $listDetailTypeStatus[$value->type_student] . " thành " . $listDetailTypeStatus[$statusUpdate],
                        'data' => json_encode([
                            'old_type_status' => $value->type_student,
                            'new_type_status' => $statusUpdate
                        ]),
                        'id_action' => request()->ip()
                    ]);
                } else {
                    Log::channel('fee-semester')->error('Cần check trạng thái sinh viên: ' . $value->user_login);
                }
            }

            DB::rollback();
            // DB::commit();
        } catch (\Exception $ex) {
            Log::channel('fee-semester')->error($ex);
            Log::channel('fee-semester')->error('Dev Check');
            Log::channel('fee-semester')->error($dataErr);
            DB::rollback();
        }
    }
}
