<?php

namespace App\Console\Commands\Fee;

use App\Models\Fu\ServiceLog;
use App\Models\Fu\ServiceRegister;
use Illuminate\Console\Command;

class ProcessEnglishFee extends Command
{
    /**
     * The name and signature of the console command.ss
     *
     * @var string
     */
    protected $signature = 'fee:proc_english';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Trừ phí tiếng anh';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        
        $this->info("Start");
        $have_order = 0;
        $orders = ServiceLog::whereIn('type', [2,23])->where('payment_status', 0)->where('status', 0)->where('term_name','Fall 2020')->get();
        $this->info($orders->count());
        foreach ($orders as $order) {
            $skill_code = ['ENT111','ENT121','ENT211','ENT221'];
            $details = ServiceRegister::where('service_log_id', $order->id)->whereIn('skill_code', $skill_code)->get();
            //$this->info($order->user_id . '|' . $details->count());
            if (!$details->count()) {
                continue;
            }
            $have_order++;
        }
        $this->info($have_order . " đơn chưa xử lý");
    }
}
