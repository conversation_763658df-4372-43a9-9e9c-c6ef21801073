<?php

namespace App\Console\Commands\Fee;


use App\Models\Fu\User;
use App\Models\EnglishDetail;
use App\Models\EnglishDetailLevel;
use App\Models\Fu\Term;
use App\Models\Fu\GroupMember;
use App\Models\Fee\Fee;
use App\Models\Fee\FeeMail;
use App\Models\Fee\FeeMailLog;
use App\Models\Fee\Transaction;
use App\Models\Fu\ServiceRegister;
use App\Models\T7\CourseResult;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessEnglishFeeNew extends Command
{
    /**
     * The name and signature of the console command.ss
     *
     * @var string
     */
    protected $signature = 'fee:process_english_fee';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Trừ phí tiếng anh';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        Log::channel('fee-semester')->info("==================================" . now()->format(' Y-m-d h:i:s ') . "==================================");
        
        $listSkillCodeEnglish = [
            1 => 'ENT111', 
            2 => 'ENT121', 
            3 => 'ENT211', 
            4 => 'ENT221'
        ];
        $lastTerm = Term::orderBy('id', 'desc')->first();
        $secondTerm = Term::where('id', '<', $lastTerm->id)
            ->orderBy('id', 'desc')
            ->first();
        
        // Lấy danh sách giảm 50% học do không xếp lớp được ở kỳ trước
        $listMailDiscountEnglish = FeeMail::where('term_id', $secondTerm->id)
            ->where('discount_english', 1)
            ->pluck('user_code')
            ->toArray();

        // Lấy danh sách sinh viên dự thu tiếng
        $listMail = FeeMail::where('term_id', $lastTerm->id)
            ->where('tieng_anh', '>', 0)
            ->where('english_status', 0)
            // ->where('user_login', 'haiptph25839')
            ->get();

        DB::beginTransaction();
        try {
            foreach ($listMail as $key => $mail) {
                if (!isset($listSkillCodeEnglish[$mail->english_level])) {
                    Log::channel('fee-semester')->info(("[$lastTerm->term_name] $mail->user_code:\t Dev Check lại sinh viên này"));
                    continue;
                }

                // Kiểm tra đã xử lý phí chưa
                if ($mail->english_status == 1) {
                    Log::channel('fee-semester')->info(("[$lastTerm->term_name] $mail->user_code:\t đã xử lý"));
                    continue;
                }

                // kiểm tra bản ghi trừ phí
                $checkEnglishFee = Transaction::where('user_login', $mail->user_login)
                    ->where('note', "Dự thu phí tiếng anh kỳ $lastTerm->term_name")
                    ->first();

                // Nếu đã trừ
                if ($checkEnglishFee) {
                    // Lấy thông tin log tiếng anh để cập nhập
                    $lastEnglish = null;
                    $english = EnglishDetail::where('user_code', $mail->user_code)->first();
                    if ($english) {
                        $lastEnglish = EnglishDetailLevel::where('english_id', $english->id)
                        ->where('term_id', '<', $lastTerm->id)
                        ->orderBy('create_time','desc')
                        ->first();
                    }

                    // lấy thông tin ví sinh viên
                    $fee = Fee::where('user_login', $mail->user_login)->first();
                    if (!$fee) { // không có ví
                        Log::channel('fee-semester')->info(("[$lastTerm->term_name] $mail->user_code:\t Không có ví"));
                        continue;
                    }

                    $user_original = User::where('user_code', $fee->user_code)->first();
                    $amount = $checkEnglishFee->amount;

                    // Kiểm tra xem được xếp lớp chưa
                    $checkLearnEnglish = GroupMember::select([
                        'group_member.member_login',
                        'list_group.id as group_id',
                        'list_group.skill_code',
                        'list_group.psubject_code',
                        'list_group.group_name',
                        'list_group.pterm_name',
                        'list_group.pterm_id'
                    ])
                    ->join('list_group', 'list_group.id', '=', 'group_member.groupid')
                    ->where('group_member.member_login', $mail->user_login)
                    ->where('list_group.pterm_id', $lastTerm->id)
                    ->whereIn('list_group.skill_code', $listSkillCodeEnglish)
                    ->where('is_virtual', 0)
                    ->first();

                    // Nếu đang được xếp lớp
                    if ($checkLearnEnglish) {
                        // lấy tiếng anh cuối học 
                        $lastLearnEnglish = CourseResult::select([
                            't7_course_result.id',
                            't7_course_result.student_login',
                            't7_course_result.psubject_code',
                            't7_course_result.skill_code',
                            't7_course_result.term_id',
                            't7_course_result.pterm_name',
                        ])
                        ->join('list_group', 'list_group.id', '=', 't7_course_result.groupid')
                        ->where('t7_course_result.skill_code', $listSkillCodeEnglish[$mail->english_level])
                        ->where('t7_course_result.student_login', $mail->user_login)
                        ->where('t7_course_result.term_id', '<', $lastTerm->id)
                        ->where('list_group.is_virtual', 0)
                        ->orderBy('t7_course_result.id', 'desc')
                        ->first();
                        
                        // Nếu có
                        if ($lastLearnEnglish) {
                            // Kiểm tra có giống tiếng anh hiện tại không (học lại)
                            if ($lastLearnEnglish->skill_code == $checkLearnEnglish->skill_code) {
                                // kiểm tra Sinh viên có học lại liền kỳ không
                                if($secondTerm->id == $lastLearnEnglish->term_id) {
                                    Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học lại 50%"));
                                    $discount = $amount / 2;
                                    
                                    // Hồi phí dự thu
                                    $fee->study_wallet = $fee->study_wallet + $amount;
                                    Transaction::create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $amount,
                                        'execute' => 1,
                                        'in_out' => 1,
                                        'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    // Kiểm tra xem có đươn không
                                    // trừ phí tiếng anh
                                    $fee->study_wallet = $fee->study_wallet - ($amount - $discount);
                                    Transaction::create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $discount,
                                        'execute' => 1,
                                        'in_out' => 0,
                                        'note' => $fee->brand_code . '|' . ($mail->ki_thu) . '|LV' . ($lastEnglish->level ?? $mail->english_level) . '|' . $lastEnglish->note,
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    // Thêm log mail
                                    FeeMailLog::create([
                                        'fee_mail_id' => $mail->mail_id,
                                        'auth' => 'system',
                                        'user_login' => $mail->user_login,
                                        'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                        'description' => "Xử lý phí tiếng anh học lại liền kỳ (50%)",
                                        'data' => json_encode([
                                            'amount' => ($discount)
                                        ]),
                                        'id_action' => request()->ip()
                                    ]);

                                    if($lastEnglish != null) {
                                        $lastEnglish->payment_status = 1;
                                        $lastEnglish->discount = $discount;
                                        $lastEnglish->save();
                                    }

                                    // thay cập nhập trạng thái tiếng anh 
                                    $mail->english_status = FeeMail::STATUS_ENGLISH_PROCES_RELEARN_50;
                                    $mail->save();
                                    $fee->save();
                                } else {
                                    Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học lại 100%"));
                                    $discount = $amount;
                                    
                                    // Hồi phí dự thu
                                    $fee->study_wallet = $fee->study_wallet + $amount;
                                    Transaction::create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $amount,
                                        'execute' => 1,
                                        'in_out' => 1,
                                        'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    // trừ phí tiếng anh
                                    $fee->study_wallet = $fee->study_wallet - $discount;
                                    Transaction::create([
                                        'user_code' => $fee->user_code,
                                        'user_login' => $fee->user_login,
                                        'type' => 'HP',
                                        'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                        'invoice_id' => 0,
                                        'amount' => $discount,
                                        'execute' => 1,
                                        'in_out' => 0,
                                        'note' => $fee->brand_code . '|' . ($mail->ki_thu) . '|LV' . ($lastEnglish->level ?? $mail->english_level) . '|' . $lastEnglish->note,
                                        'invoice_date_create' => now(),
                                        'term_name' => $lastTerm->term_name,
                                        'created_by' => 'system',
                                    ]);

                                    if($lastEnglish != null) {
                                        $lastEnglish->payment_status = 1;
                                        $lastEnglish->save();
                                    }

                                    // Thêm log mail
                                    FeeMailLog::create([
                                        'fee_mail_id' => $mail->mail_id,
                                        'auth' => 'system',
                                        'user_login' => $mail->user_login,
                                        'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                        'description' => "Xử lý phí tiếng anh học lại cách kỳ (100%)",
                                        'data' => json_encode([
                                            'amount' => ($discount)
                                        ]),
                                        'id_action' => request()->ip()
                                    ]);

                                    // Kiểm tra xem có trong danh sách sinh viên không xếp được lớp tại kỳ trước không
                                    if (in_array($fee->user_code, $listMailDiscountEnglish)) {
                                        $fee->study_wallet = $fee->study_wallet + ($discount * 0.5);
                                        Transaction::create([
                                            'user_code' => $fee->user_code,
                                            'user_login' => $fee->user_login,
                                            'type' => 'HP',
                                            'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                            'invoice_id' => 0,
                                            'amount' => ($discount * 0.5),
                                            'execute' => 1,
                                            'in_out' => 1,
                                            'note' => "Hồi phí ưu đãi 50% do không xếp được lớp tại kỳ $secondTerm->term_name",
                                            'invoice_date_create' => now(),
                                            'term_name' => $lastTerm->term_name,
                                            'created_by' => 'system',
                                        ]);

                                        FeeMailLog::create([
                                            'fee_mail_id' => $mail->mail_id,
                                            'auth' => 'system',
                                            'user_login' => $mail->user_login,
                                            'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                            'description' => "Hồi phí ưu đãi 50% do không xếp được lớp tại kỳ $secondTerm->term_name",
                                            'data' => json_encode([
                                                'amount' => ($discount * 0.5)
                                            ]),
                                            'id_action' => request()->ip()
                                        ]);
                                        Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code Học Lại Hồi phí ưu đãi 50% do không xếp được lớp tại kỳ $secondTerm->term_name"));
                                    }

                                    // thay cập nhập trạng thái tiếng anh 
                                    $mail->english_status = FeeMail::STATUS_ENGLISH_PROCES_RELEARN_100;
                                    $mail->save();
                                    $fee->save();
                                }
                            } else {
                                // học đi
                                Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học đi"));
                                $amount = $checkEnglishFee->amount;
                                $discount = $amount;
                                
                                // Hồi phí dự thu
                                $fee->study_wallet = $fee->study_wallet + $amount;
                                Transaction::create([
                                    'user_code' => $fee->user_code,
                                    'user_login' => $fee->user_login,
                                    'type' => 'HP',
                                    'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                    'invoice_id' => 0,
                                    'amount' => $amount,
                                    'execute' => 1,
                                    'in_out' => 1,
                                    'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                                    'invoice_date_create' => now(),
                                    'term_name' => $lastTerm->term_name,
                                    'created_by' => 'system',
                                ]);

                                // trừ phí tiếng anh
                                $fee->study_wallet = $fee->study_wallet - $discount;
                                Transaction::create([
                                    'user_code' => $fee->user_code,
                                    'user_login' => $fee->user_login,
                                    'type' => 'HP',
                                    'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                    'invoice_id' => 0,
                                    'amount' => $discount,
                                    'execute' => 1,
                                    'in_out' => 0,
                                    'note' => $fee->brand_code . '|' . ($mail->ki_thu) . '|LV' . ($lastEnglish->level + 1) . '|' . $lastEnglish->note,
                                    'invoice_date_create' => now(),
                                    'term_name' => $lastTerm->term_name,
                                    'created_by' => 'system',
                                ]);

                                if($lastEnglish != null) {
                                    $lastEnglish->payment_status = 1;
                                    $lastEnglish->save();
                                }


                                // Thêm log mail
                                FeeMailLog::create([
                                    'fee_mail_id' => $mail->mail_id,
                                    'auth' => 'system',
                                    'user_login' => $mail->user_login,
                                    'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                    'description' => "Xử lý phí tiếng anh học đi",
                                    'data' => json_encode([
                                        'amount' => ($discount)
                                    ]),
                                    'id_action' => request()->ip()
                                ]);

                                // Kiểm tra xem có trong danh sách sinh viên không xếp được lớp tại kỳ trước không
                                if (in_array($fee->user_code, $listMailDiscountEnglish)) {
                                    // $fee->study_wallet = $fee->study_wallet + ($discount * 0.5);
                                Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code Học đi Hồi phí ưu đãi 50% do không xếp được lớp tại kỳ $secondTerm->term_name"));
                                }

                                // thay cập nhập trạng thái tiếng anh 
                                $mail->english_status = FeeMail::STATUS_ENGLISH_PROCES_HD;
                                $mail->save();
                                $fee->save();
                            }
                        } else {
                            // Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Cần check"));
                            // Mặc định trừ 2tr6
                            // Trạng thái học sinh là HD
                            if ($user_original->study_status == 1) {
                                Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Trừ tiếng anh học đi - không rõ trạng thái trước đó"));
                                $amount = $checkEnglishFee->amount;
                                $discount = $amount;
    
                                $fee->study_wallet = $fee->study_wallet + $amount;
                                Transaction::create([
                                    'user_code' => $fee->user_code,
                                    'user_login' => $fee->user_login,
                                    'type' => 'HP',
                                    'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                    'invoice_id' => 0,
                                    'amount' => $amount,
                                    'execute' => 1,
                                    'in_out' => 1,
                                    'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                                    'invoice_date_create' => now(),
                                    'term_name' => $lastTerm->term_name,
                                    'created_by' => 'system',
                                ]);
    
                                // trừ phí tiếng anh
                                $fee->study_wallet = $fee->study_wallet - $discount;
                                Transaction::create([
                                    'user_code' => $fee->user_code,
                                    'user_login' => $fee->user_login,
                                    'type' => 'HP',
                                    'type_extension' => 'Tiếng anh|Lv' . ($mail->english_level),
                                    'invoice_id' => 0,
                                    'amount' => $discount,
                                    'execute' => 1,
                                    'in_out' => 0,
                                    'note' => $fee->brand_code . '|' . ($mail->ki_thu) . '|LV' . ($mail->english_level) . '|Tiếng anh học đi',
                                    'invoice_date_create' => now(),
                                    'term_name' => $lastTerm->term_name,
                                    'created_by' => 'system',
                                ]);

                                // Thêm log mail
                                FeeMailLog::create([
                                    'fee_mail_id' => $mail->mail_id,
                                    'auth' => 'system',
                                    'user_login' => $mail->user_login,
                                    'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                    'description' => "Xử lý phí tiếng anh học đi - không rõ trạng thái trước đó",
                                    'data' => json_encode([
                                        'amount' => ($discount)
                                    ]),
                                    'id_action' => request()->ip()
                                ]);
    
                                if($lastEnglish != null) {
                                    $lastEnglish->payment_status = 1;
                                    $lastEnglish->save();
                                }
    
                                // thay cập nhập trạng thái tiếng anh 
                                $mail->english_status = 1;
                                $mail->save();
                                $fee->save();
                            } else {
                                // Hồi phí tiếng anh cho sv đã trừ và trạng thái không phải HD
                                Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Hồi phí tiếng anh (đã xếp lớp - nhưng trạng thái khác HD)"));
                                $fee->study_wallet = $fee->study_wallet + $amount;
                                Transaction::create([
                                    'user_code' => $fee->user_code,
                                    'user_login' => $fee->user_login,
                                    'type' => 'HP',
                                    'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                                    'invoice_id' => 0,
                                    'amount' => $amount,
                                    'execute' => 1,
                                    'in_out' => 1,
                                    'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                                    'invoice_date_create' => now(),
                                    'term_name' => $lastTerm->term_name,
                                    'created_by' => 'system',
                                ]);

                                // Thêm log mail
                                FeeMailLog::create([
                                    'fee_mail_id' => $mail->mail_id,
                                    'auth' => 'system',
                                    'user_login' => $mail->user_login,
                                    'action' => FeeMailLog::ACTION_PROCESS_FEE,
                                    'description' => "Hồi phí tiếng anh Đã xếp lớp nhưng trạng thái khác HD",
                                    'data' => json_encode([
                                        'amount' => ($amount)
                                    ]),
                                    'id_action' => request()->ip()
                                ]);
        
                                $mail->english_status = FeeMail::STATUS_ENGLISH_PROCES_REFUND;
                                $fee->save();
                                $mail->save();
                            }
                        }
                    } else {
                        Log::channel('fee-semester')->info(("[$lastTerm->term_name] $fee->user_code:\t[study_wallet: $fee->study_wallet]\t Hồi phí tiếng anh do không học"));
                        $fee->study_wallet = $fee->study_wallet + $amount;
                        Transaction::create([
                            'user_code' => $fee->user_code,
                            'user_login' => $fee->user_login,
                            'type' => 'HP',
                            'type_extension' => 'Tiếng anh|Lv' . ($lastEnglish->level ?? $mail->english_level),
                            'invoice_id' => 0,
                            'amount' => $amount,
                            'execute' => 1,
                            'in_out' => 1,
                            'note' => "Hồi phí dự thu phí tiếng anh kỳ $lastTerm->term_name",
                            'invoice_date_create' => now(),
                            'term_name' => $lastTerm->term_name,
                            'created_by' => 'system',
                        ]);

                        // Thêm log mail
                        $strLog = "Hồi phí tiếng anh do không thấy được xếp lớp";
                        if ($user_original->study_status == 1) {
                            $strLog = "Hồi phí tiếng anh do không thấy được xếp lớp (HD)";
                        }

                        FeeMailLog::create([
                            'fee_mail_id' => $mail->mail_id,
                            'auth' => 'system',
                            'user_login' => $mail->user_login,
                            'action' => FeeMailLog::ACTION_PROCESS_FEE,
                            'description' => $strLog,
                            'data' => json_encode([
                                'amount' => ($amount)
                            ]),
                            'id_action' => request()->ip()
                        ]);

                        $mail->english_status = FeeMail::STATUS_ENGLISH_PROCES_REFUND;
                        $fee->save();
                        $mail->save();
                    }
                } else {
                    Log::channel('fee-semester')->info(("[$lastTerm->term_name] $mail->user_code:\t Chưa trừ phí dự thu"));
                }
            }

            // DB::rollback();
            DB::commit();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::channel('fee-semester')->error($ex);
        }
    }
}
