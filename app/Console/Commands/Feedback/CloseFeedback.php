<?php

namespace App\Console\Commands\Feedback;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\Block;
use App\Models\Fu\Term;
use App\Models\T7\Configuration;

use App\helper\FeedbackHelper;

class CloseFeedback extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:feedback-close';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'tự động đóng feedback';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('max_execution_time', -1);
        
        DB::beginTransaction();
        // L<PERSON>y danh sách lớp đủ điều kiện
        
        $currentTerm = Term::whereRaw('NOW() >= startday')
            ->whereRaw('NOW() <= endday')
            ->first();
        $currentBlock = Block::whereRaw('start_day <= CURRENT_DATE')
            ->whereRaw('end_day >= CURRENT_DATE')
            ->first();
        if (!$currentTerm) {
            return 0;
        }

        if ($currentBlock->block_name == 'Block 1') {
            $configBlock = Configuration::where('module_name', 'feedback_config_day_block_1')->first();
        } else {
            $configBlock = Configuration::where('module_name', 'feedback_config_day_block_2')->first();
        }

        $minDay = 34 + ($configBlock->variable_value ?? 0);
        $maxDay = 40 + ($configBlock->variable_value ?? 0);
        // update feedback
        $blockCheck = Block::select([        
            'id',
            'start_day',
            'term_id',
            'end_day',
            DB::raw('DATE_ADD( start_day, INTERVAL ' . $minDay . ' DAY ) AS min_day_check'),
            DB::raw('DATE_ADD( start_day, INTERVAL ' . $maxDay . ' DAY ) AS max_day_check')
        ])
        ->where('term_id', $currentTerm->id)
        ->whereRaw('DATE_ADD( start_day, INTERVAL ' . $minDay . ' DAY ) <= CURRENT_DATE')
        ->whereRaw('DATE_ADD( start_day, INTERVAL ' . $maxDay . ' DAY ) >= CURRENT_DATE')
        ->first();

        $feedbackHelper = new FeedbackHelper();
        if ($blockCheck) {
            $feedbackHelper->updateFeedback();
        }

        if ($blockCheck) {
            $feedbackHelper->closeFeedback($blockCheck);
        }
    }
}
