<?php

namespace App\Console\Commands\Feedback;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Block;
use App\Models\Fu\Term;
use App\Models\Fu\Activity;
use App\Models\Fu\Feedback;
use App\Models\T7\Configuration;
use App\helper\FeedbackHelper;



class CreateFeedback extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:feedback-create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'tự động tạo feedback';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /**
         * "Hệ thống tự động mở lấy GPA cho tất cả các lớp theo nguyên tắc:
         * - Với môn học trong 1 Block:
         * + Bắt đầu mở từ tuần thứ 4 của Block.
         * + Kéo dài 11 ngày (từ ngày thứ 22 - 33 của Block).
         * - Với môn học cả kỳ:
         * + Bắt đầu mở từ tuần thứ 6 của Kỳ.
         * + Kéo dài 11 ngày (từ ngày thứ 43 - 54 của Kỳ)."
         */
        // Lấy danh sách lớp đủ điều kiện
        
        $currentTerm = Term::whereRaw('NOW() >= startday')
            ->whereRaw('NOW() <= endday')
            ->first();
        $currentBlock = Block::whereRaw('start_day <= CURRENT_DATE')
            ->whereRaw('end_day >= CURRENT_DATE')
            ->first();
        if (!$currentTerm) {
            return 0;
        }
    
        if ($currentBlock->block_name == 'Block 1') {
            $configBlock = Configuration::where('module_name', 'feedback_config_day_block_1')->first();
        } else {
            $configBlock = Configuration::where('module_name', 'feedback_config_day_block_2')->first();
        }

        $minDay = 22 + ($configBlock->variable_value ?? 0);
        $maxDay = 33 + ($configBlock->variable_value ?? 0);
        // $minDayEng = 42 + ($configEnglish->variable_value ?? 0);
        // $maxDayEng = 54 + ($configEnglish->variable_value ?? 0);
        $blockCheck = Block::select([        
            'id',
            'start_day',
            'term_id',
            'end_day',
            DB::raw('DATE_ADD( start_day, INTERVAL ' . $minDay . ' DAY ) AS min_day_check'),
            DB::raw('DATE_ADD( start_day, INTERVAL ' . $maxDay . ' DAY ) AS max_day_check')
        ])
        ->where('term_id', $currentTerm->id)
        ->whereRaw('DATE_ADD( start_day, INTERVAL ' . $minDay . ' DAY ) <= CURRENT_DATE')
        ->whereRaw('DATE_ADD( start_day, INTERVAL ' . $maxDay . ' DAY ) >= CURRENT_DATE')
        ->first();

        $feedbackHelper = new FeedbackHelper();
        if ($blockCheck) {
            $feedbackHelper->createFeedback($blockCheck);
        }
    }
}
