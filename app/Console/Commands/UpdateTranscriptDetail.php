<?php

namespace App\Console\Commands;

use App\Http\Controllers\Admin\TranscriptController;
use App\Http\Lib;
use App\Models\Transcript;
use App\Models\TranscriptDetail;
use App\Models\Fu\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateTranscriptDetail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transcript:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cập nhật chi tiết bảng điểm';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        
        // <PERSON><PERSON><PERSON> tra bị doublue ở sổ điểm thì xóa đi
        $listDataDouble = Transcript::select('transcripts.*')
            ->join('user', 'user.user_login', 'transcripts.user_login')
            ->join(
                DB::raw('(
            SELECT min(transcripts.id) as min_id, user_login
            FROM transcripts
            GROUP BY user_code, user_login
            HAVING count(id) > 1) tbl_custom'),
                function ($join) {
                    $join->on('transcripts.id', '>', 'min_id');
                    $join->on('tbl_custom.user_login', '=', 'transcripts.user_login');
                }
            )
            ->whereIn('user.study_status', [1, 3, 10, 11, 13])
            ->get();

        foreach ($listDataDouble as $key => $value) {
            TranscriptDetail::where('transcript_id', $value->id)->delete();
            $value->delete();
        }

        $this->info("Command $this->signature is running...");
        Lib::logDiscord("info", "Command $this->signature is running...");
        try {
            $countUser = User::where('user_level', 3)
                ->whereIn('study_status', [1, 3, 10, 11, 13])->count();
            $num = ceil($countUser / 1000);
            $this->info("Thực hiện $num lần");
            Lib::logDiscord("info", "Thực hiện $num lần");
            $per = 0;
            for ($i = 0; $i < $num * 1000; $i += 1000) {
                $per++;
                TranscriptController::syncFromCourseResultSchedule($i, 1000);
                $this->info("Hoàn tất lần $per, skip $i limit 1000");
                Lib::logDiscord("info", "Hoàn tất lần $per, skip $i limit 1000");
            }

            // Quét lại danh sách sinh viên bị double sổ điểm
            $this->info("Quét sổ điểm double");
            $listUserDoubleTranscript = User::select('user.*', DB::raw('count( period_subject.id ) AS count1'), 't2.count2')
                ->leftJoin('period_subject', 'user.curriculum_id', '=', 'period_subject.curriculum_id')
                ->join(DB::raw("(SELECT
                user.user_login,
                count( transcript_details.id ) AS count2
            FROM user
                LEFT JOIN transcripts ON transcripts.user_login = user.user_login
                JOIN transcript_details ON transcript_details.transcript_id = transcripts.id
                GROUP BY user.user_login) t2"), function ($join) {
                    $join->on('t2.user_login', '=', 'user.user_login');
                })
                ->where('user.user_level', 3)
                ->whereNotIn('user.study_status', [8])
                ->groupBy('user.user_login')
                ->havingRaw('count1 < t2.count2')
                ->get();

            $this->info("có " . count($listUserDoubleTranscript) . " bị double");
            TranscriptController::syncGradePoint($listUserDoubleTranscript);
        } catch (\Exception $e) {
            Log::channel('transcript-sync')->error($e);
            $this->error('Lỗi transcript:update');
            Lib::logDiscord("error", 'Lỗi transcript:update');
        }

        $this->info("Command $this->signature success");
        Lib::logDiscord("info", "Command $this->signature success");
    }
}
