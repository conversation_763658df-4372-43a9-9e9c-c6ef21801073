<?php

namespace App\Console\Commands;

use App\Models\Fu\Attendance;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\SystemLog;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\Fu\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class removeStudentFromGroup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove-student-from-group';
    private $list = [];
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'remove student from group';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        
        foreach ($this->list as $item) {
            try {
                DB::beginTransaction();
                $group_name = $item[1];
                $user_code = $item[0];
                $subject_code = $item[2];

                $group_id = Group::where('group_name', $group_name)->where('psubject_code', $subject_code)->orderBy('id', 'desc')->first();
                if (!$group_id) {
                    break;
                }
                $group_id = $group_id->id;
                $user_login = User::where('user_code', $user_code)->first();
                if (!$user_login) {
                    break;
                }
                $user_login = $user_login->user_login;
                $content = "remove member $user_login from group $group_id";

                $this->info('Start remove ' . $item[0] . '(' . $user_login . ')' . ' from group ' . $item[1] . ' (group_id = ' . $group_id . ') subject_code ' . $item[2]);

                GroupMember::where('groupid', $group_id)->where('member_login', $user_login)->delete();
                Attendance::where('groupid', $group_id)->where('user_login', $user_login)->delete();
                CourseGrade::where('groupid', $group_id)->where('login', $user_login)->delete();
                CourseResult::where('groupid', $group_id)->where('student_login', $user_login)->delete();
                SystemLog::create([
                    'object_name' => 'group',
                    'actor' => 'admin',
                    'log_time' => Carbon::now(),
                    'action' => 'remove member',
                    'description' => $content,
                    'object_id' => $group_id,
                    'brief' => 'remove member',
                    'from_ip' => '*********',
                    'relation_login' => $user_login,
                    'relation_id' => $group_id,
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ]);

                DB::commit();
            } catch (\Throwable $th) {
                DB::rollBack();
                $this->error($th->getMessage());
                break;
            }
        }
    }
}
