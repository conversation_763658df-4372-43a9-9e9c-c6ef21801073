<?php

namespace App\Console\Commands;

use App\Models\Fu\Attendance;
use App\Models\Fu\GroupMember;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\Fu\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class removeListStudentFromAp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove-students';
    private $list_student_code = [];

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Carbon::setLocale('vi');
        $date = Carbon::now()->format('Y-m-d-H-i-s');
        
        $context = "remove-students{$date} \n ";
        $list_student_login = User::whereIn('user_code',$this->list_student_code)->select('user_login')->pluck('user_login')->toArray();
        DB::beginTransaction();
        try {
            User::whereIn('user_code',$this->list_student_code)->delete();
            GroupMember::whereIn('member_login',$list_student_login)->delete();
            Attendance::whereIn('user_login',$list_student_login)->delete();
            CourseGrade::whereIn('login',$list_student_login)->delete();
            CourseResult::whereIn('student_login',$list_student_login)->delete();
            DB::commit();
            $context .= "\n ".json_encode($this->list_student_code) . " \n";
            $context .= "Remove success \n";
            $this->info('Removed');
            Storage::disk('local')->append("remove-student-$date.txt", $context);
        } catch (\Exception $e) {
            DB::rollback();
            $this->error($e->getLine() . " : " . $e->getMessage());
        }
    }
}
