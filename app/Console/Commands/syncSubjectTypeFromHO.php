<?php

namespace App\Console\Commands;

use App\Models\Fu\Subject;
use Illuminate\Console\Command;

class syncSubjectTypeFromHO extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync-subject-type-ho';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        
        $this->info('Sync Subject From HO');
        $this->info('Start');
        $subject_type_ho = Subject::get();
        $total = count($subject_type_ho);
        $this->info('Total Subject Type: '.$total);
        $processed = 0;
        foreach ($subject_type_ho as $subject) {
            try {
                Subject::where('subject_code', $subject->subject_code)->update(['subject_type' => $subject->subject_type]);
                $processed++;
            } catch (\Throwable $th) {
                $this->error('Error '.$subject->subject_code.' : '.$th->getMessage());
            }
        }
        $this->info('Processed: '.$processed);
        $this->info('End');
    }
}
