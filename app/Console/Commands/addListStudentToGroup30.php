<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Fu\Attendance;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\SystemLog;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\Fu\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;

class addListStudentToGroup30 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add-student-to-group30';
    private $list = array();
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        foreach ($this->list as $item) {
            try {
                DB::beginTransaction();
                $group_name = $item[1];
                $user_code = $item[0];
                $subject_code = $item[2];
                $this->info('Start remove ' . $item[0] . ' from group ' . $item[1] . ' subject_code ' . $item[2]);
                $group = Group::where('group_name', $group_name)->where('psubject_code',$subject_code)->orderBy('id','desc')->first();
                if (!$group) {
                    break;
                }
                $leader_login = $group->teacher;
                $group_id = $group->id;
                $user_login = User::where('user_code', $user_code)->first();
                if (!$user_login) {
                    break;
                }
                $user_login = $user_login->user_login;
                $content = "add member $user_login to group $group_id";

                $check = GroupMember::insertGetId([
                    'groupid' => $group_id,
                    'member_login' => $user_login,
                    'user_code' => $user_code,
                    'date' => date('Y-m-d'),
                    'note' => 'Thêm sv vào lớp chạy quá 30%',
                    'attend_time' => 0,
                    'fee_status' => 0,
                    'fee_code' => 0,
                    'current_status' => 0,
                    'skill_code' => 0,
                    'period_id' => 0,
                    'period_fee_status' => 0,
                    'fee_due_date' => '2025-01-01',
                    'fee_submit_date' => '2025-01-01',
                    'lock_date' => '2025-01-01',
                    'curriculum_id' => 0,
                    'subject_id' => 0,
                    'term_id' => 0,
                    'group_name' => $group_name,
                    'subject_code' => $subject_code,
                    'period_ordering' => 0,
                    'full_name' => '',
                    'study_status' => 0,
                    'start_date' => '2025-01-01',
                    'end_date' => '2025-01-01',
                    'temp' => 0,
                    'is_hoc_lai' => 0,
                    'group_member_status' => 0,
                    'IsCancelled' => 0,
                    'leader_login' => $leader_login,
                    'loai' => '',
                    'note_comment' => '',
                    'time_evaluate' => date('Y-m-d'),
                    'time_ evaluate' => date('Y-m-d'),
                ]);
                if (!$check) {
                    $this->error($content . ' FAILED!');
                    throw new Exception($content);
                }

                SystemLog::create([
                    'object_name' => 'group',
                    'actor' => 'admin',
                    'log_time' => Carbon::now(),
                    'action' => 'add member',
                    'description' => $content,
                    'object_id' => $group_id,
                    'brief' => 'add member',
                    'from_ip' => '*********',
                    'relation_login' => $user_login,
                    'relation_id' => $group_id,
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ]);

                DB::commit();
            } catch (\Throwable $th) {
                DB::rollBack();
                $this->error($th->getMessage());
                break;
                
            }
            
        }
    }
}
