<?php

namespace App\Console\Commands\ExamSchedule;

use App\helper\ExamHelper;
use App\Models\Fu\Group;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class SyncExamEos extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule-exam:sync-eos {term_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Đồng bộ buổi thi eos';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */


    public function handle()
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        
        $termId = $this->argument('term_id');
        

        // lấy danh sách lớp cần ph<PERSON>i đồng bộ
        $listGroupIdSync = Group::select([
                'list_group.id',
                'list_group.body_id'
            ])
            ->leftJoin('activity', 'list_group.id', 'activity.groupid')
            ->leftJoin('group_eos', 'list_group.id', 'group_eos.group_id')
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->join('t7_syllabus_plan',function ($q) {
                $q->on('activity.psyllabus_id', 't7_syllabus_plan.syllabus_id')
                ->on('activity.course_slot', 't7_syllabus_plan.course_session');
            })
            ->whereNotNull('group_eos.group_id')
            ->whereRaw('list_group.id IN (
                SELECT list_group.id 
                FROM group
                LEFT JOIN group_eos ON group_eos.group_id = list_group.id
                WHERE list_group.pterm_id = ?
                AND is_virtual = 0
                GROUP BY group_eos.group_id 
            )', [$termId])
            ->where('session_type.is_exam', 1)
            ->whereRaw('activity.`day` >= CURRENT_DATE')
            ->groupBy('list_group.id')
            ->get()->toArray();

        $groupIdByCourse = [];
        foreach ($listGroupIdSync as $value) {
            $groupIdByCourse[$value['body_id']][] = $value['id'];
        }
        
        // Duyệt từng course một
        // $count = 0;
        foreach ($groupIdByCourse as $courseId => $value) {
            // $count++;
            // if (!in_array($count, [4, 5, 6, 7, 8, 9])) continue;
            Log::channel('exam_schedule')->info(['data_send_eos' => [$termId, $courseId]]);
            $dataCheck = ExamHelper::SyncExamEos($termId, $courseId, $value);
            if (is_array($dataCheck)) {
                Log::channel('exam_schedule')->error([
                    'user_check_eos' => 'dev',
                    'msg' => $dataCheck
                ]);
            }
        }

    }
}
