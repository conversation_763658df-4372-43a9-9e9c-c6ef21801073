<?php

namespace App\Console\Commands;

use App\Models\Fu\Activity;
use App\Models\Fu\Attendance;
use App\Models\Fu\Block;

use App\Models\Fu\Group;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\T7\CourseResult;
use App\Models\T7\GradeSyllabus;
use App\Models\T7\SyllabusPlan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;

class syncAttendanceByGroup extends Command
{
    
    private $group_id = 0;
    private $date = 0;
    private $to_date = 0;
    private $updated_at = 0;

    
    const CO_MAT = 1;
    const VANG_MAT = 0;
    const OVER_TIME = 120;
    const DA_DIEM_DANH = 1;
    const CHUA_DIEM_DANH = 0;
    const START = 1;
    const TRUOT_DIEM_DANH = -1;
    const DANG_HOC = 0;
    const TIME_SUB = 30;
    const TIME_ADD = 15;
    const searchBySemester = 1;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'resync-attendance-by-group {group_id} {date} {to-date} {--last-time-moddify=0}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->group_id = $this->argument('group_id');
        $this->date = $this->argument('date');
        $this->to_date = $this->argument('to-date');
        $this->updated_at = $this->option('last-time-moddify');
        if (!is_numeric($this->group_id)) {
            $this->error('Group id is not valid');
            return 0;
        }
        if(!preg_match('/^\d{4}-\d{2}-\d{2}$/', $this->date) && $this->date != 0) {
            $this->error('Date is not valid');
            return 0;
        }
        if(!preg_match('/^\d{4}-\d{2}-\d{2}$/', $this->to_date) && $this->to_date != 0) {
            $this->error('To date is not valid');
            return 0;
        }
        if(!preg_match('/^\d{4}-\d{2}-\d{2}$/', $this->updated_at) && $this->updated_at != 0) {
            $this->updated_at = 0;
        }
        $this->info('Group id: '.$this->group_id);
        $this->info('Date: '.$this->date);
        $this->info('To date: '.$this->to_date);
        $this->info('Updated date: '.$this->updated_at);
        $list_group_id = Attendance::query();
        $list_group_id->join('activity', 'activity.id', '=', 'attendance.activity_id');
        $list_group_id->join('group_member', function($query){
            $query->on('group_member.groupid', '=', 'activity.groupid')
                    ->on('group_member.member_login', '=', 'attendance.user_login');
        });
        if ($this->group_id > 0) {
            $list_group_id = $list_group_id->where('activity.groupid', $this->group_id);
        }
        if ($this->date != 0) {
            $list_group_id = $list_group_id->where('activity.day','>=', $this->date);
        }
        if ($this->to_date != 0) {
            $list_group_id = $list_group_id->where('activity.day','<=', $this->to_date);
        }
        if ($this->updated_at != 0) {
            $list_group_id = $list_group_id->where('attendance.lastmodified_time','>=', $this->updated_at);
        }
        $list_group_id = $list_group_id->get(['activity.groupid as group_id','activity.id as activity_id','group_member.member_login']);
        $this->info('Total raw: '.count($list_group_id));
        $list_group_activity = new \stdClass();
        foreach ($list_group_id as $key => $value) {
            if(!isset($list_group_activity->{$value->group_id})){
                $list_group_activity->{$value->group_id} = new \stdClass();
            }
            $list_group_activity->{$value->group_id}->activity_id[] = $value->activity_id;
            $list_group_activity->{$value->group_id}->member_login[] = $value->member_login;
        }
        $total = 0;
        foreach ($list_group_activity as $key => $value) {
            $total++;
        }
        $this->info('Total student login: '.$total);
        $this->info('Start sync attendance');
        $this->info('--------------------------------');
        $done = 0;
        foreach ($list_group_activity as $group_id => $group_member_login) {
            try {
                $this->info('processing : '.($done/$total) * 100 .'%');
                $this->info('Group id: '.$group_id);
                $group = Group::find($group_id);
                $subject = Subject::findOrFail($group->psubject_id);
                Activity::whereIn('id', $group_member_login->activity_id)
                ->update([
                        'done' => self::START,
                        'short_subject_name' => $subject->short_name
                        ]);
                $this->info('Update activity done');
                $this->info('update attendance of '.count($group_member_login->member_login));
                foreach ($group_member_login->member_login as $member_login) {
                    $this->ProcesssyncDataForReAttendanceCourseResult($member_login, $group);
                }
                $this->info('done sync attendance of group '.$group_id);
                $this->info('--------------------------------');
            } catch (\Throwable $th) {
                $this->error('Error: '. $th->getLine() .' : '. $th->getMessage());
                $this->info('--------------------------------');
                break;
            }
            $done++;
            system('clear');
        }
        return 0;
    }

    private function ProcesssyncDataForReAttendanceCourseResult($student_user_login, $group)
    {
        DB::beginTransaction();
        try {
            $activity_count = [];
            $activity_detail = [];
            $activity_done = 0;
            $activity_not_done = 0;

            $checkUserdRemote2 = Term::where('id', $group->pterm_id)
            ->where('startday', '>', '2022-05-01')
            ->count();
    
            $syllabus_plan = SyllabusPlan::select('id')
            ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
            ->where('t7_syllabus_plan.syllabus_id', $group->syllabus_id)
            ->where('session_type.is_exam', 0)
            ->count();
            $syllabus = GradeSyllabus::findOrFail($group->syllabus_id);


            $activities = Activity::query()
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->where('activity.groupid', $group->id)
            ->where('session_type.is_exam', 0)
            ->get();
            foreach ($activities as $activity) {
                if (now() > $activity->day) {
                    $activity_done += 1;
                }
                else {
                    $activity_not_done += 1;
                }
                $activity_count[] = $activity->id;
                $activity_detail[$activity->id] = [
                    'course_slot' => $activity->course_slot,
                    'slot' => $activity->slot,
                ];
            }

            $attendance_details = [];
            $attendance_absent = 0;
            $diem_danh = 0;
            $attendances = Attendance::where('user_login', $student_user_login)->where('groupid', $group->id)->whereIn('activity_id', $activity_count)->get();
            foreach ($attendances as $attendance) {
                if ($attendance->val == 0) {
                    $attendance_absent += 1;
                }
                $attendance_details[] = "$attendance->day:" . $activity_detail[$attendance->activity_id]['course_slot'] . ':' . $activity_detail[$attendance->activity_id]['slot'] . ':' . "$attendance->lastmodifier_login:$attendance->val:$attendance->description";
            }
            if ($syllabus_plan == 0) {
                $absent_progress = false;
                $absent_progress_doing = false;
            }
            else {
                $absent_progress = ($attendance_absent * 100 / $syllabus_plan) > (100 - $syllabus->attendance_cutoff);
                $absent_progress_doing = ($attendance_absent * 100 / $syllabus_plan) <= (100 - $syllabus->attendance_cutoff);
            }


            if ($absent_progress) {
                $diem_danh = self::TRUOT_DIEM_DANH;
            }
            if ($diem_danh) {
                if ($absent_progress_doing) {
                    $diem_danh = self::DANG_HOC;
                }
            }
            try {
                $result = CourseResult::where('student_login', $student_user_login)->where('groupid', $group->id)->update([
                    'val' => $diem_danh,
                    'attendance_detail' => implode(',', $attendance_details),
                    'attendance_absent' => $attendance_absent,
                    'done_activity' => $activity_done,
                    'attendance_cutoff' => $syllabus->attendance_cutoff,
                    'modifier_login' => 'system',
                    'not_done_activity' => $activity_not_done,
                ]);
            } catch (QueryException $exception) {
                DB::rollBack();
                throw new \Exception(' result ProcesssyncDataForReAttendanceCourseResult < 1 $student_user_login=' . $student_user_login . ' $group->id=' . $group->id . 'error=' . $exception->getMessage());
            }
            DB::commit();
        }
        catch (\Throwable $th) {
            Log::error("-------------- start err ProcesssyncDataForReAttendanceCourseResult ----------------");
            Log::error(' result ProcesssyncDataForReAttendanceCourseResult < 1 $student_user_login=' . $student_user_login . ' $group->id=' . $group->id);
            Log::error($th);
            Log::error("-------------- end err ProcesssyncDataForReAttendanceCourseResult ----------------");
            DB::rollBack();
            throw new \Exception($th->getFile() . ' : ' . $th->getLine() . ' : ' . $th->getMessage());
        }
    }
}
