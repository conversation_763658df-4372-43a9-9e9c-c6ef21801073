<?php

namespace App\Console\Commands\Grade;

use Illuminate\Console\Command;
use App\Models\Fu\Attendance;
use App\Models\Fu\Course;
use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\SystemLog;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\T7\Grade;
use App\Models\T7\GradeGroup;
use App\Models\T7\GradeSyllabus;
use App\Models\T7\SyllabusPlan;
use App\Models\Fu\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AddZeroPoint extends Command
{
    /**
     * The name and signature of the console command.
     * type_scan = bydata: là theo database
     * type_scan = byfile: là theo dữ liệu theo file
     * 
     * @var string
     */
    protected $signature = 'add-zero-point {type_scan=bydata}';

    const DAT = 1;
    const KHONG_DAT = 0;
    const TRUOT_DIEM_DANH = -1;
    const CO_DI_THI = 1;
    const CO_DI_THI_2 = 2;
    const KHONG_DI_THI = 0;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        
        $typeScan = $this->argument('type_scan');
        // Lấy danh sách sinh viên
        /*
         * $listUser = [
         *    'th' => [
         *       [
         *           'Kỳ',
         *           'Mã sinh viên',
         *           'Tên lớp',
         *           'Mã Môn',
         *       ],
         *    ],
         *    'tc' => [....]
         * ]
        */

        $listUser = [];
        $termIgnore = null;
        Log::channel('add_zero_point')->info("Bắt đầu phủ điểm cho sinh viên");
        Log::channel('add_zero_point')->info("Bắt đầu lấy dữ liệu tại thời điểm: " . time());
        if ($typeScan == 'byfile') {
            $listUser = [];
        } else {
            $getTermIgnore = Term::whereRaw('startday <= CURRENT_DATE')
                ->whereRaw('endday >= CURRENT_DATE')
                ->first();
            if (!$getTermIgnore) {
                $getTermIgnore = Term::orderBy('id', 'DESC')
                    ->first();
            }

            $listUser = GroupMember::select([
                    'group_member.member_login',
                    'list_group.id AS group_id',
                    'list_group.pterm_id',
                    'list_group.pterm_name',
                    'list_group.group_name',
                    'list_group.skill_code',
                    'list_group.psubject_code',
                    'list_group.start_date',
                    'list_group.end_date',
                    'course.id AS course_id',
                    't7_grade.id AS grade_id',
                    't7_grade_group.grade_group_name',
                    't7_grade.minimum_required',
                    't7_grade.weight',
                    't7_grade.grade_name',
                    't7_grade.grade_group_id',
                    't7_grade_group.grade_group_name',
                    't7_grade_group.weight AS grade_group_weight',
                    't7_grade_group.minimum_required AS grade_group_minimum_required',
                    't7_grade.syllabus_id',
                    't7_grade.subject_id',
                    't7_grade.subject_name',
                    't7_grade.is_final_exam',
                    't7_grade.allow_resit AS "is_resit"',
                    't7_grade.master_grade'
                ])
                ->leftJoin('list_group', 'list_group.id', '=', 'group_member.groupid')
                ->leftJoin('user', 'user.user_login', '=', 'group_member.member_login')
                ->join('course', 'list_group.body_id', '=', 'course.id')
                ->join('t7_grade', 'course.syllabus_id', '=', 't7_grade.syllabus_id')
                ->join('t7_grade_group', 't7_grade_group.id', '=', 't7_grade.grade_group_id')
                ->leftJoin('t7_course_grade', function($q) {
                    return $q->on('t7_course_grade.login','group_member.member_login')
                    ->on('t7_course_grade.groupid','list_group.id')
                    ->on('t7_course_grade.grade_id','t7_grade.id');
                })
                ->where('list_group.is_virtual', 0)
                ->where('course.term_id', '!=', $getTermIgnore->id)
                ->where('t7_grade.master_grade', 0)
                ->where('t7_grade.bonus_type', 0)
                ->whereNotIn('user.study_status', [8])
                ->whereNull('t7_course_grade.id')
                ->orderBy('list_group.id')
                ->orderBy('group_member.member_login')
                // ->limit(500)
                ->get();
        }
        
        Log::channel('add_zero_point')->info("Lấy dữ liệu xong tại thời điểm: " . time());
        $listGroupSync = [];
        $listAddPoint = [];
        // Duyệt danh sách user
        $now = Carbon::now()->format('Y-m-d H:m:s');
        foreach ($listUser as $key => $value) {
            // Lấy danh sách điểm còn thiếu
            $listAddPoint[] = [
                'point' => [
                    'course_id' => $value->course_id,
                    'grade_id' => $value->grade_id,
                    'grade_minimum_required' => $value->minimum_required,
                    'grade_weight' => $value->weight,
                    'grade_name' => $value->grade_name,
                    'groupid' => $value->group_id,
                    'login' => $value->member_login,
                    'val' => 0,
                    'comment' => '',
                    'creator_login' => 'dev',
                    'modifier_login' => 'dev',
                    'grade_group_id' => $value->grade_group_id,
                    'grade_group_name' => $value->grade_group_name,
                    'grade_group_weight' => $value->grade_group_weight,
                    'grade_group_minimum_required' => $value->grade_group_minimum_required,
                    'syllabus_id' => $value->syllabus_id,
                    'subject_id' => $value->subject_id,
                    'is_used' => 0,
                    'subject_name' => $value->subject_name,
                    'course_group_name' => '',
                    'subject_code' => '',
                    'term_id' => $value->pterm_id,
                    'term_name' => $value->pterm_name,
                    'is_final' => $value->is_final_exam,
                    'is_resit' => $value->is_resit,
                    'master_grade' => $value->master_grade,
                    'token' => substr(md5($value->grade_group_id . $value->syllabus_id), 0, 13),
                    'group_val' => 0,
                    'temp' => 0,
                    'locked' => 1,
                ],
                'log' => [
                    'object_name' => 'group',
                    'actor' => 'dev',
                    'log_time' => $now,
                    'action' => 'insert',
                    'description' => "insert grade: " . $value->grade_name . " for student " . $value->member_login . " with value: 0 (dev Phủ điểm)",
                    'object_id' => $value->group_id,
                    'brief' => 'grade',
                    'from_ip' => '127.0.0.1',
                    'relation_login' => $value->member_login,
                    'relation_id' => $value->group_id,
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ]
            ];
        }
    
        $limit = 200;
        Log::channel('add_zero_point')->info("Số lượng sinh viên xử lý trong 1 lần là: " . $limit);
        $numPage = ceil(sizeof($listAddPoint)/$limit);
        for($i = 0; $i < $numPage; $i++) {
            Log::channel('add_zero_point')->info("Đã xử lý " . ($i * $limit) . "");
            $dataProcess = array_slice($listAddPoint, ($i * $limit), $limit);
            DB::beginTransaction();
            $listGroupMemberSync = [];
            try {
                $listPoint = array_column($dataProcess, 'point');
                $listLog = array_column($dataProcess, 'log');
                CourseGrade::insert($listPoint);
                SystemLog::insert($listLog);

                $listGroupSync = array_values(array_unique(array_column($listPoint, 'groupid')));
                foreach ($listPoint as $dataPoint) {
                    $listGroupMemberSync[$dataPoint['groupid']][] = $dataPoint['login'];
                }

                DB::commit();
                // DB::rollback();
                Log::channel('add_zero_point')->info("Đã hoàn thành import cho $limit trường hợp");
            } catch (\Throwable $th) {
                Log::channel('add_zero_point')->error($th);
                Log::channel('add_zero_point')->error("listStudentError");
                Log::channel('add_zero_point')->error($dataProcess);
                DB::rollBack();
            }

            DB::beginTransaction();
            try {
                // Duyệt lớp chạy lại sổ điểm
                Log::channel('add_zero_point')->info("Tiến hành đồng bộ thông tin lớp");
                foreach ($listGroupSync as $key => $groupId) {
                    Log::channel('add_zero_point')->info("lớp " . ($groupId));
                    echo "$groupId\n";
                    $member_detail = [];
                    $temp_grade_group = [];
                    $temp_grade = [];
                    $diem_thuong_id = [];
                    $status_group = 0;
                    $tong_dau_diem_thi = 0;
                    $group = Group::with('groupMembers')->find($groupId);
                    $syllabus = GradeSyllabus::findOrFail($group->syllabus_id);
                    $syllabus_minimum = $syllabus->minimum_required;
            
                    $syllabus_plan = SyllabusPlan::select('id')
                        ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
                        ->where('t7_syllabus_plan.syllabus_id', $group->syllabus_id)
                        ->where('session_type.is_exam', 0)
                        ->count();
            
                    $grade_groups = GradeGroup::where('syllabus_id', $group->syllabus_id)->where('subject_id', $group->psubject_id)->get();
                    $grades_table = Grade::where('syllabus_id', $group->syllabus_id)->get();
                    $tong_dau_diem = $grades_table->count();
                    $tong_dau_diem_khong_co_diem_thuong = $grades_table->where('bonus_type', 0)->where('master_grade', 0)->count();
                    $id_diem_thuong = $grades_table->where('bonus_type', 1)->pluck('id');
                    $subject = Subject::findOrFail($group->psubject_id);
                    foreach ($grade_groups as $grade_group) {
                        CourseGrade::where('grade_group_id', $grade_group->id)->where('groupid', $groupId)->update([
                            'grade_group_weight' => $grade_group->weight,
                            'grade_group_minimum_required' => $grade_group->minimum_required,
                            'grade_group_name' => $grade_group->grade_group_name,
                        ]);
                        $temp_grade_group[$grade_group->id] = [
                            'grade_name' => $grade_group->grade_group_name,
                            'point' => 0,
                            'total_point' => 0,
                            'weight' => $grade_group->weight,
                            'number_grade' => $grade_group->number_grade,
                            'minimum_required' => $grade_group->minimum_required,
                        ];
                    }
            
                    foreach ($grades_table as $grade) {
                        CourseGrade::where('grade_id', $grade->id)->where('groupid', $groupId)->update([
                            'grade_weight' => $grade->weight,
                            'grade_minimum_required' => $grade->minimum_required,
                            'grade_name' => $grade->grade_name,
                        ]);
            
                        if ($grade->is_final_exam && $grade->master_grade == 0) {
                            $tong_dau_diem_thi += 1;
                        }
            
                        $temp_grade[$grade->id] = [
                            'bonus_type' => $grade->bonus_type,
                        ];
            
                        if ($grade->bonus_type) {
                            $diem_thuong_id[$grade->id] = $grade->id;
                        }
                    }
            
                    $course_grades = CourseGrade::where('groupid', $group->id)->orderBy('master_grade')->get();
                    $tong_diem_hoan_thanh_cua_lop = $course_grades->whereNotIn('grade_id', $id_diem_thuong)->groupBy('grade_id')->count();
                    foreach ($course_grades as $course_grade) {
                        $member_detail[$course_grade->login][] = [
                            'grade_name' => $course_grade->grade_name,
                            'grade_id' => $course_grade->grade_id,
                            'grade_group_id' => $course_grade->grade_group_id,
                            'point' => $course_grade->val,
                            'master_grade' => $course_grade->master_grade,
                            'weight' => $course_grade->grade_weight,
                            'minimum_required' => $course_grade->grade_minimum_required,
                            'is_final' => $course_grade->is_final,
                            'is_resit' => $course_grade->is_resit,
                            'grade_group_weight' => $course_grade->grade_group_weight,
                        ];
                    }
            
                    foreach ($group->groupMembers as $member) {
                        $temp = $temp_grade_group;
                        $user_login = $member->member_login;
                        // Loại bỏ danh sách sinh viên không thuộc danh sách
                        if (!in_array($user_login, ($listGroupMemberSync[$groupId] ?? []))) {
                            continue;
                        }

                        //            echo "$user_login<br>";
                        $total_point = 0;
                        $bonus_point = 0;
                        $status_subject = 0;
                        $grade_pass = 1;
                        $exam_finish = 0;
                        $exam_finish_grade = [];
                        $tong_diem_hoan_thanh = 0;
                        $tong_diem_thuong_hoan_thanh = 0;
                        $grade_array = [];
                        $di_thi = self::KHONG_DI_THI;
                        $gradeGroupIdOfFinalScore = null;
                        $gradeIdOfFinalScore = null;
                        $flagCheckHaveResit = false;
                        $course_result = $this->createOrUpdateCourseResult($group, $member->member_login, $subject);
                        if (isset($member_detail[$member->member_login])) {
                            $current_status_subject = $course_result->val;
                            $grades = $member_detail[$member->member_login];
                            $temp_grade_detail = [];
                            $final_total_point = 0;
            
                            foreach ($grades as $grade) {
                                if (!isset($diem_thuong_id[$grade['grade_id']])) {
                                    $tong_diem_hoan_thanh += 1;
                                } else {
                                    $tong_diem_thuong_hoan_thanh += 1;
                                }
            
                                $master_grade = $grade['master_grade'];
                                $grade_array[] = $grade['grade_name'] . ':' . $grade['weight'] . ':' . $grade['point'] . ':' . $grade['grade_id'];
                                if ($master_grade == 0) {
                                    if ($temp_grade[$grade['grade_id']]['bonus_type'] == 1) {
                                        $bonus_point += $grade['point'];
                                    } else {
                                        $temp_grade_detail[$grade['grade_group_id']][$grade['grade_id']] = ($grade['point'] * $grade['weight']);
                                        if (!isset($temp[$grade['grade_group_id']]['point'])) {
                                            $temp[$grade['grade_group_id']]['point'] = ($grade['point'] * $grade['weight']);
                                        } else {
                                            $temp[$grade['grade_group_id']]['point'] += ($grade['point'] * $grade['weight']);
                                        }
                                    }
                                } else {
                                    $grade_pass = 1;
                                    CourseGrade::where('groupid', $group->id)->where('login', $user_login)->where('grade_id', $master_grade)->update([
                                        'is_used' => -1
                                    ]);
                                    $temp_grade_detail[$grade['grade_group_id']][$master_grade] = ($grade['point'] * $grade['weight']);
                                    $temp[$grade['grade_group_id']]['point'] = ($grade['point'] * $grade['weight']);
                                }
            
                                if (!isset($temp[$grade['grade_group_id']]['total_point'])) {
                                    $temp[$grade['grade_group_id']]['total_point'] = ($grade['point']);
                                } else {
                                    $temp[$grade['grade_group_id']]['total_point'] += ($grade['point']);
                                }
            
                                // check + diem final 1 hay final 2nd vao tong
                                if ($grade['is_final'] === 1) {
                                    if ($grade['is_resit'] < 1) {
                                        $final_total_point = ($grade['point']);
                                        $gradeGroupIdOfFinalScore = $grade['grade_group_id'];
                                        $gradeIdOfFinalScore = $grade['grade_id'];
                                    } else {
            
                                        if ($grade['point'] !== null) {
                                            $temp[$grade['grade_group_id']]['total_point'] -= $final_total_point;
                                            $flagCheckHaveResit = true;
                                        }
                                    }
                                }
            
                                if ($grade['minimum_required'] != 0 && $grade['minimum_required'] > $grade['point']) {
                                    $grade_pass = 0;
                                }
            
                                if ($grade['is_final'] && $grade['point']) {
                                    $di_thi = self::CO_DI_THI;
                                    $status_group = 1;
                                }
            
                                if ($grade['is_resit'] == 1) {
                                    $di_thi = self::CO_DI_THI_2;
                                }
            
                                if ($grade['is_final'] && $grade['is_resit'] == 1) {
                                    $status_group = 2;
                                }
            
                                if ($grade['is_final'] && $grade['is_resit'] == 0) {
                                    $exam_finish += 1;
                                    $exam_finish_grade[$grade['grade_id']] = $grade['grade_id'];
                                }
                            }
            
                            foreach ($temp_grade_detail as $key => $sub_item) {
                                if (isset($temp[$key]['point'])) {
                                    if ($key === $gradeGroupIdOfFinalScore && !$flagCheckHaveResit) {
                                        $temp[$key]['point'] = array_sum($sub_item);
                                    }
                                }
                            }
            
                            // check nhóm đầu điểm
                            foreach ($temp as $key => $item) {
                                if ($item['weight'] == 0) {
                                    $point = 0;
                                } else {
                                    $point = $item['point'] / $item['weight'];
                                }
            
                                // $point = round($point, 1);
                                $pointCheck = round($point, 1);
                                CourseGrade::where('login', $user_login)->where('groupid', $group->id)->where('grade_group_id', $key)->update([
                                    'group_val' => $point,
                                ]);
            
                                $total_point += ($point * $item['weight']) / 100;
                                if ($item['minimum_required'] != 0 && $item['minimum_required'] > $pointCheck) {
                                    $grade_pass = 0;
                                }
                            }
            
                            $total_point = $total_point + $bonus_point;
                            if ($total_point > 10) {
                                $total_point = 10;
                            }
            
                            if ($total_point) {
                                $total_point = round($total_point, 1);
                            }
            
                            $grade_array = implode('$', $grade_array);
                            if ($grade_pass && $total_point >= $syllabus_minimum) {
                                $status_subject = self::DAT;
                            } else {
                                $status_subject = self::KHONG_DAT;
                            }
            
                            if ($current_status_subject < self::KHONG_DAT) {
                                $di_thi = self::CO_DI_THI;
                            }
            
                            if ($course_result->attendance_absent == '') {
                                $course_result->attendance_absent = 0;
                            }
            
                            //                echo "EXAM_FINISH:$exam_finish<br>";
                            //                echo "TONG_DIEM_THI:$tong_dau_diem_thi<br>";
                            //                echo "$status_subject<br>";
                            if ($exam_finish < $tong_dau_diem_thi && $status_subject >= 0) {
                                $status_subject = self::KHONG_DAT;
                            }
            
                            if (count($exam_finish_grade) < $tong_dau_diem_thi && $status_subject > 0) {
                                $status_subject = self::KHONG_DAT;
                                $di_thi = self::KHONG_DI_THI;
                            }
            
                            if ($tong_diem_hoan_thanh_cua_lop < $tong_dau_diem_khong_co_diem_thuong && $status_subject > 0) {
                                $status_subject = self::KHONG_DAT;
                                $di_thi = self::KHONG_DI_THI;
                            }
            
                            if ($tong_diem_hoan_thanh_cua_lop == $tong_dau_diem_khong_co_diem_thuong && $tong_diem_hoan_thanh > 0) {
                                $di_thi = self::CO_DI_THI_2;
                            }
                            //                echo "$grade_pass<br>";
                            //                echo "$status_subject<br>";
                        }
            
                        if ($di_thi == 2) {
                            $status_group = 1;
                        }
            
                        $checkAttendance = $course_result->attendance_absent == 0 ? 0 : ($syllabus_plan ? ($course_result->attendance_absent * 100 / $syllabus_plan) : 0);
                        // dd("$syllabus_plan ? ($course_result->attendance_absent * 100 / $syllabus_plan) : 0) > (100 - $syllabus->attendance_cutoff) ? self::TRUOT_DIEM_DANH : $status_subject");
                        $status_subject = $checkAttendance > (100 - $syllabus->attendance_cutoff) ? self::TRUOT_DIEM_DANH : $status_subject;
                        $course_result->grade = $total_point;
                        $course_result->grade_detail = $grade_array;
                        $course_result->is_finish = $di_thi;
                        $course_result->val = $status_subject;
                        $course_result->attendance_cutoff = $syllabus->attendance_cutoff;
                        $course_result->minimum_required = $syllabus->minimum_required;
                        $course_result->psubject_name = $group->psubject_name;
                        $course_result->start_date = $group->start_date;
                        $course_result->end_date = $group->end_date;
                        $course_result->psubject_code = $group->psubject_code;
                        $course_result->subject_id = $group->psubject_id;
                        //            $course_result->skill_code = $group->skill_code;
                        $course_result->total_session = $syllabus_plan;
                        $course_result->total_exam = $tong_dau_diem_thi;
                        $course_result->done_exam = count($exam_finish_grade);
                        $course_result->taken_exam = $exam_finish;
                        $course_result->total_grade = $tong_dau_diem;
                        $course_result->done_grade = $tong_diem_hoan_thanh_cua_lop + $id_diem_thuong->count();
                        $course_result->save();
                    }
            
                    if ($group->type == 1) {
                        $group->finished = $status_group;
                        $group->save();
                    }
                }

                DB::commit();
                Log::channel('add_zero_point')->info("Đã hoàn thành Đồng bộ thông tin lịch sử học");
                // DB::rollback();
            } catch (\Throwable $th) {
                Log::channel('add_zero_point')->error($th);
                Log::channel('add_zero_point')->error("listGroupError");
                Log::channel('add_zero_point')->error($listGroupSync);
                DB::rollBack();
            }


        }
    }
    
    public function createOrUpdateCourseResult($group, $user_login, $subject)
    {
        $result = CourseResult::where('student_login', $user_login)->where('groupid', $group->id)->first();
        if (!$result) {
            $result = new CourseResult();
            $result->course_id = request()->course_id;
            $result->subject_id = $group->psubject_id;
            $result->student_login = $user_login;
            $result->groupid = $group->id;
            $result->source = 2;
            $result->term_id = $group->pterm_id;
            $result->pgroup_name = $group->group_name;
            $result->syllabus_id = $group->syllabus_id;
            $result->skill_code = $subject->skill_code;
            // $result->save();
        }

        $result->source = 2;
        $result->subject_id = $group->psubject_id;
        $result->term_id = $group->pterm_id;
        $result->pterm_name = $group->pterm_name;
        $result->pgroup_name = $group->group_name;
        $result->syllabus_id = $group->syllabus_id;
        $result->skill_code = $subject->skill_code;
        $result->number_of_credit = $subject->num_of_credit;
        $result->start_date = $group->start_date;
        $result->end_date = $group->end_date;
        $result->save();

        return $result;
    }
}
