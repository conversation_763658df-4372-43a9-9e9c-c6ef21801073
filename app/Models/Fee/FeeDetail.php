<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;

class FeeDetail extends Model
{
    protected $table = 'fee_details';
    protected $fillable = [
        'fee_id',
        'debt_id',
        'user_code',
        'user_login',
        'term_id',
        'ki_thu',
        'type_fee',
        'amount',
        'discount',
        'version'
    ];

    public function __construct(array $attributes = [])
    {

        parent::__construct($attributes);
    }

    public function typeName()
    {
        $type_name = '';
        $type_fee = $this->type_fee;
        if ($type_fee == 1) {
            $type_name = '<PERSON><PERSON> học kỳ';
        } else if ($type_fee == 2) {
            $type_name = '<PERSON><PERSON> sách';
        } else if ($type_fee == 3 || $type_fee == 4) {
            $type_name = 'Tiếng anh';
        } else if ($type_fee == 5) {
            $type_name = 'Bổ sung';
        }

        return $type_name;
    }

    /**
     * Lấy trạng thái thanh toán từ bảng student_debts
     */
    public function getStatusAttribute()
    {
        return $this->studentDebt ? $this->studentDebt->status : 0;
    }

    /**
     * Kiểm tra đã thanh toán chưa
     */
    public function isPaid()
    {
        return $this->status == 1; // DEBT_STATUS_PAID
    }

    /**
     * Relationship với StudentDebt
     */
    public function studentDebt()
    {
        return $this->belongsTo(StudentDebt::class, 'debt_id');
    }

    /**
     * Relationship với Fee (nếu có)
     */
    public function fee()
    {
        return $this->belongsTo(Fee::class, 'fee_id');
    }
}
