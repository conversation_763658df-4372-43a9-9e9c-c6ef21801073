<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WalletActivityLog extends Model
{
    use HasFactory;

    protected $table = 'wallet_activity_logs';
    public $timestamps = false; // Chỉ có created_at

    protected $fillable = [
        'wallet_id',
        'user_code',
        'action',
        'actor',
        'actor_type',
        'old_data',
        'new_data',
        'changes',
        'description',
        'amount_involved',
        'transaction_reference',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'old_data' => 'array',
        'new_data' => 'array',
        'changes' => 'array',
        'amount_involved' => 'decimal:2',
        'created_at' => 'datetime'
    ];

    // Action constants
    const ACTION_CREATE = 'create';
    const ACTION_LOCK = 'lock';
    const ACTION_UNLOCK = 'unlock';
    const ACTION_UPDATE_BALANCE = 'update_balance';
    const ACTION_CHANGE_INFO = 'change_info';
    const ACTION_DEPOSIT = 'deposit';
    const ACTION_WITHDRAW = 'withdraw';
    const ACTION_TRANSFER = 'transfer';

    // Actor type constants
    const ACTOR_ADMIN = 'admin';
    const ACTOR_SYSTEM = 'system';
    const ACTOR_STUDENT = 'student';

    /**
     * Relationship với StudentWallet (nếu có)
     */
    public function wallet()
    {
        return $this->belongsTo(StudentWallet::class, 'wallet_id');
    }

    /**
     * Lấy tên action dễ đọc
     */
    public function getActionNameAttribute()
    {
        $actionNames = [
            self::ACTION_CREATE => 'Tạo ví',
            self::ACTION_LOCK => 'Khóa ví',
            self::ACTION_UNLOCK => 'Mở khóa ví',
            self::ACTION_UPDATE_BALANCE => 'Cập nhật số dư',
            self::ACTION_CHANGE_INFO => 'Thay đổi thông tin',
            self::ACTION_DEPOSIT => 'Nạp tiền',
            self::ACTION_WITHDRAW => 'Rút tiền',
            self::ACTION_TRANSFER => 'Chuyển tiền'
        ];

        return $actionNames[$this->action] ?? $this->action;
    }

    /**
     * Lấy tên actor type dễ đọc
     */
    public function getActorTypeNameAttribute()
    {
        $actorTypeNames = [
            self::ACTOR_ADMIN => 'Quản trị viên',
            self::ACTOR_SYSTEM => 'Hệ thống',
            self::ACTOR_STUDENT => 'Sinh viên'
        ];

        return $actorTypeNames[$this->actor_type] ?? $this->actor_type;
    }

    /**
     * Lấy số tiền formatted
     */
    public function getFormattedAmountAttribute()
    {
        if (!$this->amount_involved) {
            return null;
        }
        
        return number_format($this->amount_involved, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Tạo log cho wallet activity
     */
    public static function createLog($walletId, $userCode, $action, $actor, $description = null, $oldData = null, $newData = null, $amountInvolved = null, $transactionRef = null)
    {
        $changes = null;
        if ($oldData && $newData) {
            $changes = array_diff_assoc($newData, $oldData);
        }

        return self::create([
            'wallet_id' => $walletId,
            'user_code' => $userCode,
            'action' => $action,
            'actor' => $actor,
            'actor_type' => self::determineActorType($actor),
            'old_data' => $oldData,
            'new_data' => $newData,
            'changes' => $changes,
            'description' => $description,
            'amount_involved' => $amountInvolved,
            'transaction_reference' => $transactionRef,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Xác định loại actor
     */
    private static function determineActorType($actor)
    {
        if ($actor === 'system') {
            return self::ACTOR_SYSTEM;
        }
        
        // Có thể thêm logic để phân biệt admin và student
        if (strpos($actor, 'admin_') === 0 || strpos($actor, 'staff_') === 0) {
            return self::ACTOR_ADMIN;
        }
        
        return self::ACTOR_STUDENT;
    }

    /**
     * Scope cho action cụ thể
     */
    public function scopeAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope cho user cụ thể
     */
    public function scopeForUser($query, $userCode)
    {
        return $query->where('user_code', $userCode);
    }

    /**
     * Scope cho wallet cụ thể
     */
    public function scopeForWallet($query, $walletId)
    {
        return $query->where('wallet_id', $walletId);
    }

    /**
     * Scope cho khoảng thời gian
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope cho các action liên quan đến tiền
     */
    public function scopeMoneyActions($query)
    {
        return $query->whereIn('action', [
            self::ACTION_DEPOSIT,
            self::ACTION_WITHDRAW,
            self::ACTION_TRANSFER,
            self::ACTION_UPDATE_BALANCE
        ]);
    }
}
