<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\Term;
use App\Traits\LogsDebtActivity;

class StudentDebt extends Model
{
    use LogsDebtActivity;
    protected $table = 'student_debts';

    protected $fillable = [
        'user_code',
        'user_login',
        'term_id',
        'term_name',
        'fee_type_id',
        'amount',
        'paid_amount',
        'status',
        'description',
        'created_by',
        'canceled_by',
        'canceled_at'
    ];

    public function term()
    {
        return $this->belongsTo(Term::class, 'term_id');
    }

    public function feeType()
    {
        return $this->belongsTo(FeeType::class, 'fee_type_id');
    }

    public function payments()
    {
        return $this->hasMany(DebtPayment::class, 'debt_id');
    }

    public function feeDetails()
    {
        return $this->hasMany(FeeDetail::class, 'debt_id');
    }
}
