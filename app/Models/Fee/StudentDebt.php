<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;
use App\Models\Fu\Term;
use App\Traits\LogsDebtActivity;

class StudentDebt extends Model
{
    use LogsDebtActivity;
    protected $table = 'student_debts';

    protected $fillable = [
        'user_code',
        'user_login',
        'term_id',
        'term_name',
        'fee_type_id',
        'fee_id',
        'ki_thu',
        'type_fee',
        'original_amount',
        'discount_amount',
        'discount_percentage',
        'discount_reason',
        'amount',
        'paid_amount',
        'version',
        'status',
        'description',
        'created_by',
        'canceled_by',
        'canceled_at'
    ];

    public function term()
    {
        return $this->belongsTo(Term::class, 'term_id');
    }

    public function feeType()
    {
        return $this->belongsTo(FeeType::class, 'fee_type_id');
    }

    public function payments()
    {
        return $this->hasMany(DebtPayment::class, 'debt_id');
    }

    public function fee()
    {
        return $this->belongsTo(Fee::class, 'fee_id');
    }

    /**
     * L<PERSON>y tên loại phí chi tiết
     */
    public function getTypeFeeNameAttribute()
    {
        $typeNames = [
            1 => 'Phí học kỳ',
            2 => 'Phí sách',
            3 => 'Tiếng anh cơ bản',
            4 => 'Tiếng anh nâng cao',
            5 => 'Bổ sung'
        ];

        return $typeNames[$this->type_fee] ?? 'Không xác định';
    }

    /**
     * Tính toán số tiền sau discount
     */
    public function calculateFinalAmount()
    {
        if ($this->discount_percentage > 0) {
            return $this->original_amount * (1 - $this->discount_percentage / 100);
        }

        return $this->original_amount - $this->discount_amount;
    }

    /**
     * Kiểm tra có discount không
     */
    public function hasDiscount()
    {
        return $this->discount_amount > 0 || $this->discount_percentage > 0;
    }

    /**
     * Lấy số tiền còn lại phải trả
     */
    public function getRemainingAmountAttribute()
    {
        return $this->amount - $this->paid_amount;
    }

    /**
     * Kiểm tra đã thanh toán đủ chưa
     */
    public function isFullyPaid()
    {
        return $this->paid_amount >= $this->amount;
    }

    /**
     * Lấy phần trăm đã thanh toán
     */
    public function getPaidPercentageAttribute()
    {
        if ($this->amount <= 0) {
            return 0;
        }

        return round(($this->paid_amount / $this->amount) * 100, 2);
    }

    /**
     * Lấy số tiền discount formatted
     */
    public function getFormattedDiscountAmountAttribute()
    {
        if (!$this->hasDiscount()) {
            return null;
        }

        return number_format($this->discount_amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Lấy số tiền gốc formatted
     */
    public function getFormattedOriginalAmountAttribute()
    {
        return number_format($this->original_amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Lấy số tiền cuối formatted
     */
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Lấy số tiền còn lại formatted
     */
    public function getFormattedRemainingAmountAttribute()
    {
        return number_format($this->remaining_amount, 0, ',', '.') . ' VNĐ';
    }
}
