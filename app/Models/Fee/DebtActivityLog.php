<?php

namespace App\Models\Fee;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DebtActivityLog extends Model
{
    use HasFactory;

    protected $table = 'debt_activity_logs';
    public $timestamps = false; // Chỉ có created_at

    protected $fillable = [
        'debt_id',
        'user_code',
        'action',
        'actor',
        'actor_type',
        'old_data',
        'new_data',
        'changes',
        'description',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'old_data' => 'array',
        'new_data' => 'array',
        'changes' => 'array',
        'created_at' => 'datetime'
    ];

    // Action constants
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_CANCEL = 'cancel';
    const ACTION_PAYMENT = 'payment';
    const ACTION_RESTORE = 'restore';
    const ACTION_DELETE = 'delete';

    // Actor type constants
    const ACTOR_ADMIN = 'admin';
    const ACTOR_SYSTEM = 'system';
    const ACTOR_STUDENT = 'student';

    /**
     * Relationship với StudentDebt
     */
    public function debt()
    {
        return $this->belongsTo(StudentDebt::class, 'debt_id');
    }

    /**
     * Lấy tên action dễ đọc
     */
    public function getActionNameAttribute()
    {
        $actionNames = [
            self::ACTION_CREATE => 'Tạo công nợ',
            self::ACTION_UPDATE => 'Cập nhật công nợ',
            self::ACTION_CANCEL => 'Hủy công nợ',
            self::ACTION_PAYMENT => 'Thanh toán',
            self::ACTION_RESTORE => 'Khôi phục công nợ',
            self::ACTION_DELETE => 'Xóa công nợ'
        ];

        return $actionNames[$this->action] ?? $this->action;
    }

    /**
     * Lấy tên actor type dễ đọc
     */
    public function getActorTypeNameAttribute()
    {
        $actorTypeNames = [
            self::ACTOR_ADMIN => 'Quản trị viên',
            self::ACTOR_SYSTEM => 'Hệ thống',
            self::ACTOR_STUDENT => 'Sinh viên'
        ];

        return $actorTypeNames[$this->actor_type] ?? $this->actor_type;
    }

    /**
     * Tạo log cho debt activity
     */
    public static function createLog($debtId, $action, $actor, $description = null, $oldData = null, $newData = null)
    {
        $changes = null;
        if ($oldData && $newData) {
            $changes = array_diff_assoc($newData, $oldData);
        }

        return self::create([
            'debt_id' => $debtId,
            'user_code' => $newData['user_code'] ?? $oldData['user_code'] ?? null,
            'action' => $action,
            'actor' => $actor,
            'actor_type' => self::determineActorType($actor),
            'old_data' => $oldData,
            'new_data' => $newData,
            'changes' => $changes,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Xác định loại actor
     */
    private static function determineActorType($actor)
    {
        if ($actor === 'system') {
            return self::ACTOR_SYSTEM;
        }
        
        // Có thể thêm logic để phân biệt admin và student
        // Ví dụ: check prefix của user_login
        if (strpos($actor, 'admin_') === 0 || strpos($actor, 'staff_') === 0) {
            return self::ACTOR_ADMIN;
        }
        
        return self::ACTOR_STUDENT;
    }

    /**
     * Scope cho action cụ thể
     */
    public function scopeAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope cho user cụ thể
     */
    public function scopeForUser($query, $userCode)
    {
        return $query->where('user_code', $userCode);
    }

    /**
     * Scope cho debt cụ thể
     */
    public function scopeForDebt($query, $debtId)
    {
        return $query->where('debt_id', $debtId);
    }

    /**
     * Scope cho khoảng thời gian
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}
