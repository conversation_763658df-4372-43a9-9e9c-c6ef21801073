<?php

namespace App\Imports;

use App\Models\Fee\StudentDebt;
use App\Models\Fu\Term;
use App\Models\Fee\FeeType;
use App\Models\Fu\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class DebtBulkImport implements ToCollection, WithHeadingRow, WithValidation, SkipsOnFailure, WithBatchInserts, WithChunkReading
{
    use SkipsFailures;

    protected $results = [
        'total_rows' => 0,
        'success_count' => 0,
        'error_count' => 0,
        'errors' => [],
        'created_debts' => []
    ];

    protected $termCache = [];
    protected $feeTypeCache = [];
    protected $isFirstChunk = true;

    public function __construct()
    {
        // Pre-load terms and fee types for better performance
        $this->loadCacheData();
    }

    /**
     * Process the collection of rows
     */
    public function collection(Collection $rows)
    {
        // Filter out completely empty rows before processing
        $nonEmptyRows = $rows->filter(function ($row) {
            return $this->isRowNotEmpty($row);
        });

        // Only update total_rows if we have non-empty rows
        if ($nonEmptyRows->isNotEmpty()) {
            $this->results['total_rows'] += $nonEmptyRows->count();

            if ($this->isFirstChunk) {
                Log::info("Starting debt import with {$nonEmptyRows->count()} non-empty rows in first chunk (filtered from {$rows->count()} total rows)");
                $this->isFirstChunk = false;
            } else {
                Log::debug("Processing additional chunk with {$nonEmptyRows->count()} non-empty rows (Total so far: {$this->results['total_rows']})");
            }
        } else {
            Log::debug('No non-empty rows in chunk, skipping');
            return;
        }

        DB::beginTransaction();
        try {
            $processedCount = 0;
            foreach ($rows as $row) {
                // Calculate actual row number considering all previous chunks
                $actualRowNumber = $this->results['total_rows'] - $nonEmptyRows->count() + $processedCount + 2;

                // Skip empty rows
                if (!$this->isRowNotEmpty($row)) {
                    Log::debug("Skipping empty row {$actualRowNumber}");
                    continue;
                }

                $this->processRow($row, $actualRowNumber);
                $processedCount++;
            }

            DB::commit();
            Log::debug("Chunk processed. Current totals - Created: {$this->results['success_count']}, Errors: {$this->results['error_count']}");
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Debt import chunk failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process a single row
     */
    protected function processRow($row, $rowNumber)
    {
        try {
            Log::debug("Processing row {$rowNumber}", ['raw_data' => $row]);

            // Validate and clean data
            $cleanData = $this->validateAndCleanRow($row, $rowNumber);
            if (!$cleanData) {
                Log::warning("Row {$rowNumber} failed validation");
                return; // Skip this row due to validation errors
            }

            Log::debug("Row {$rowNumber} validation passed", ['clean_data' => $cleanData]);

            // Check for duplicate debt
            if ($this->isDuplicateDebt($cleanData)) {
                $this->addError($rowNumber, $row, 'Công nợ đã tồn tại cho sinh viên này trong kỳ học này');
                Log::warning("Row {$rowNumber} is duplicate debt");
                return;
            }

            // Tính toán discount cho Excel import
            $originalAmount = floatval($cleanData['original_amount'] ?? $cleanData['amount']);
            $discountAmount = floatval($cleanData['discount_amount'] ?? 0);
            $discountPercentage = floatval($cleanData['discount_percentage'] ?? 0);

            // Đảm bảo mặc định discount_percentage = 0
            if ($discountPercentage < 0 || $discountPercentage > 100) {
                $discountPercentage = 0;
            }

            // Tính số tiền cuối cùng
            $finalAmount = $originalAmount;
            if ($discountPercentage > 0) {
                $discountAmount = round($originalAmount * ($discountPercentage / 100), 2);
                $finalAmount = $originalAmount - $discountAmount;
            } elseif ($discountAmount > 0) {
                $finalAmount = $originalAmount - $discountAmount;
                $discountPercentage = 0;
            } else {
                $discountAmount = 0;
                $discountPercentage = 0;
            }

            // Create debt record với discount
            $debtData = [
                'user_code' => $cleanData['user_code'],
                'user_login' => $cleanData['user_login'],
                'term_id' => $cleanData['term_id'],
                'term_name' => $cleanData['term_name'],
                'fee_type_id' => $cleanData['fee_type_id'],
                'original_amount' => $originalAmount,
                'discount_amount' => $discountAmount,
                'discount_percentage' => $discountPercentage,
                'discount_reason' => $cleanData['discount_reason'] ?? null,
                'amount' => $finalAmount, // Số tiền thực tế phải trả
                'paid_amount' => 0,
                'status' => $cleanData['status'] ?? 0,
                'description' => $cleanData['notes'] ?? null,
                'created_by' => Auth::user()->user_login ?? 'system'
            ];

            Log::debug("Creating debt with data", $debtData);

            // Thêm các trường từ fee_details vào debtData
            $typeFeeMapping = [
                1 => 1, // Học phí -> Phí học kỳ
                2 => 5, // Phí ký túc xá -> Bổ sung
                3 => 5, // Phí bảo hiểm -> Bổ sung
                4 => 1, // Phí học lại -> Phí học kỳ
                5 => 1, // Phí thi lại -> Phí học kỳ
                6 => 5, // Phí khác -> Bổ sung
            ];
            $typeFee = $typeFeeMapping[$cleanData['fee_type_id']] ?? 5;

            $debtData['fee_id'] = $cleanData['fee_id'] ?? null;
            $debtData['ki_thu'] = $this->getKiThuFromTerm($cleanData['term_id']);
            $debtData['type_fee'] = $typeFee;
            $debtData['version'] = 1;

            $debt = StudentDebt::create($debtData);

            // Không cần tạo FeeDetail nữa - tất cả thông tin đã có trong StudentDebt

            $this->results['success_count']++;
            $this->results['created_debts'][] = $debt->id;

            Log::debug("Created debt for {$cleanData['user_code']} - Amount: {$cleanData['amount']}");
        } catch (\Exception $e) {
            $this->addError($rowNumber, $row, 'Lỗi tạo công nợ: ' . $e->getMessage());
            Log::error("Error processing row {$rowNumber}: " . $e->getMessage(), [
                'row_data' => $row,
                'exception' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Validate and clean row data
     */
    protected function validateAndCleanRow($row, $rowNumber)
    {
        $errors = [];

        // Required fields validation - handle both string and number from Excel
        $userCode = trim(strtoupper(strval($row['user_code'] ?? '')));
        $userLogin = trim(strtolower(strval($row['user_login'] ?? '')));
        $termName = trim(strval($row['term_name'] ?? ''));
        $feeTypeName = trim(strval($row['fee_type_name'] ?? ''));
        $amount = floatval($row['amount'] ?? 0);

        if (empty($userCode)) $errors[] = 'Mã sinh viên không được để trống';
        if (empty($userLogin)) $errors[] = 'Tài khoản đăng nhập không được để trống';
        if (empty($termName)) $errors[] = 'Tên kỳ học không được để trống';
        if (empty($feeTypeName)) $errors[] = 'Loại phí không được để trống';
        if ($amount <= 0) $errors[] = 'Số tiền phải lớn hơn 0';

        // Check if user exists
        if (!empty($userCode) && !empty($userLogin)) {
            $user = User::where('user_code', $userCode)
                ->where('user_login', $userLogin)
                ->first();



            if (!$user) {
                // Check if user_code exists with different login
                $userByCode = User::where('user_code', $userCode)->first();
                $userByLogin = User::where('user_login', $userLogin)->first();



                if (!$userByCode && !$userByLogin) {
                    $errors[] = "Không tìm thấy sinh viên với mã '{$userCode}' và tài khoản '{$userLogin}'";
                } elseif (!$userByCode) {
                    $errors[] = "Không tìm thấy sinh viên với mã '{$userCode}'";
                } elseif (!$userByLogin) {
                    $errors[] = "Không tìm thấy tài khoản đăng nhập '{$userLogin}'";
                } else {
                    $errors[] = "Mã sinh viên '{$userCode}' và tài khoản '{$userLogin}' không khớp với nhau";
                }
            }
        }

        // Validate term exists
        $term = $this->findTerm($termName);
        if (!$term) {
            $errors[] = "Không tìm thấy kỳ học: {$termName}";
            Log::warning("Term not found", [
                'term_name' => $termName,
                'available_terms' => array_keys($this->termCache)
            ]);
        }

        // Validate fee type exists
        $feeType = $this->findFeeType($feeTypeName);
        if (!$feeType) {
            $errors[] = "Không tìm thấy loại phí: {$feeTypeName}";
            Log::warning("Fee type not found", [
                'fee_type_name' => $feeTypeName,
                'available_fee_types' => array_keys($this->feeTypeCache)
            ]);
        }

        if (!empty($errors)) {
            $this->addError($rowNumber, $row, $errors);
            return null;
        }

        // Validate discount fields từ Excel
        $originalAmount = floatval($row['original_amount'] ?? $amount);
        $discountAmount = floatval($row['discount_amount'] ?? 0);
        $discountPercentage = floatval($row['discount_percentage'] ?? 0);
        $discountReason = trim($row['discount_reason'] ?? '');

        // Validate discount
        if ($discountPercentage < 0 || $discountPercentage > 100) {
            $errors[] = 'Phần trăm giảm giá phải từ 0 đến 100';
        }
        if ($discountAmount < 0 || $discountAmount > $originalAmount) {
            $errors[] = 'Số tiền giảm giá không hợp lệ';
        }
        if ($discountPercentage > 0 && $discountAmount > 0) {
            $errors[] = 'Chỉ được chọn một trong hai: phần trăm giảm giá hoặc số tiền giảm giá';
        }

        // Kiểm tra lại errors sau khi validate discount
        if (!empty($errors)) {
            $this->addError($rowNumber, $row, $errors);
            return null;
        }

        return [
            'user_code' => $userCode,
            'user_login' => $userLogin,
            'term_id' => $term->id,
            'term_name' => $term->term_name,
            'fee_type_id' => $feeType->id,
            'amount' => $amount,
            'original_amount' => $originalAmount,
            'discount_amount' => $discountAmount,
            'discount_percentage' => $discountPercentage,
            'discount_reason' => $discountReason,
            'status' => intval($row['status'] ?? 0),
            'notes' => trim($row['notes'] ?? '')
        ];
    }

    /**
     * Check if debt already exists
     */
    protected function isDuplicateDebt($data)
    {
        return StudentDebt::where('user_code', $data['user_code'])
            ->where('term_id', $data['term_id'])
            ->where('fee_type_id', $data['fee_type_id'])
            ->where('status', '!=', 2) // Not cancelled
            ->exists();
    }

    /**
     * Load cache data for better performance
     */
    protected function loadCacheData()
    {
        // Cache terms
        Term::all()->each(function ($term) {
            $this->termCache[strtolower($term->term_name)] = $term;
        });

        // Cache fee types
        FeeType::where('is_active', true)->get()->each(function ($feeType) {
            $this->feeTypeCache[strtolower($feeType->name)] = $feeType;
        });

        Log::info("Cached " . count($this->termCache) . " terms and " . count($this->feeTypeCache) . " fee types");
    }

    /**
     * Find term by name (case insensitive)
     */
    protected function findTerm($termName)
    {
        return $this->termCache[strtolower($termName)] ?? null;
    }

    /**
     * Find fee type by name (case insensitive)
     */
    protected function findFeeType($feeTypeName)
    {
        return $this->feeTypeCache[strtolower($feeTypeName)] ?? null;
    }

    /**
     * Add error to results
     */
    protected function addError($rowNumber, $row, $message)
    {
        $this->results['error_count']++;

        // Handle both array and string messages
        $errorMessage = is_array($message) ? $message : [$message];

        $this->results['errors'][] = [
            'row' => $rowNumber,
            'data' => $row,
            'errors' => $errorMessage,
            'message' => implode('; ', $errorMessage) // For backward compatibility
        ];
    }

    /**
     * Check if a row is not empty (has at least one non-empty value)
     */
    protected function isRowNotEmpty($row)
    {
        if (!is_array($row) && !($row instanceof Collection)) {
            return false;
        }

        $values = $row instanceof Collection ? $row->toArray() : $row;

        foreach ($values as $value) {
            if ($value !== null && trim(strval($value)) !== '') {
                return true;
            }
        }

        return false;
    }

    /**
     * Laravel Excel validation rules - only apply to non-empty rows
     */
    public function rules(): array
    {
        return [
            'user_code' => 'nullable', // Accept both string and number
            'user_login' => 'nullable', // Accept both string and number
            'term_name' => 'nullable|string',
            'fee_type_name' => 'nullable|string',
            'amount' => 'nullable|numeric|min:0.01',
        ];
    }

    /**
     * Conditional validation - only validate if row is not empty
     */
    public function withValidator($validator)
    {
        $validator->sometimes(['user_code', 'user_login', 'term_name', 'fee_type_name', 'amount'], 'required', function ($input) {
            // Only require fields if the row has any data
            return $this->isRowNotEmpty($input);
        });
    }

    /**
     * Batch size for better performance
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * Chunk size for reading large files
     */
    public function chunkSize(): int
    {
        return 500;
    }

    /**
     * Handle validation failures from Laravel Excel
     */
    public function onFailure(\Maatwebsite\Excel\Validators\Failure ...$failures)
    {
        foreach ($failures as $failure) {
            // Skip validation failures for completely empty rows
            if (!$this->isRowNotEmpty($failure->values())) {
                Log::debug("Skipping validation failure for empty row {$failure->row()}");
                continue;
            }

            $this->results['error_count']++;
            $this->results['errors'][] = [
                'row' => $failure->row(),
                'data' => $failure->values(),
                'errors' => $failure->errors(),
                'message' => 'Validation failed: ' . implode(', ', $failure->errors())
            ];

            Log::warning("Laravel Excel validation failed", [
                'row' => $failure->row(),
                'attribute' => $failure->attribute(),
                'errors' => $failure->errors(),
                'values' => $failure->values()
            ]);
        }
    }

    // Method createFeeDetailForDebt đã bỏ vì không cần tạo FeeDetail nữa
    // Tất cả thông tin đã được lưu trực tiếp trong StudentDebt

    /**
     * Lấy ki_thu từ term_id
     */
    protected function getKiThuFromTerm($termId)
    {
        try {
            $term = Term::find($termId);
            if ($term && isset($term->ordering)) {
                return $term->ordering;
            }
            return 1; // Default
        } catch (\Exception $e) {
            return 1; // Default
        }
    }

    /**
     * Get import results
     */
    public function getResults()
    {
        return $this->results;
    }
}
