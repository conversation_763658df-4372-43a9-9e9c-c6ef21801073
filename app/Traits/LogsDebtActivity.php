<?php

namespace App\Traits;

use App\Models\Fee\DebtActivityLog;
use Illuminate\Support\Facades\Auth;

trait LogsDebtActivity
{
    /**
     * Boot the trait
     */
    protected static function bootLogsDebtActivity()
    {
        // Log khi tạo debt mới
        static::created(function ($debt) {
            self::logDebtActivity(
                $debt->id,
                DebtActivityLog::ACTION_CREATE,
                self::getCurrentActor(),
                'Tạo công nợ mới',
                null,
                $debt->toArray()
            );
        });

        // Log khi cập nhật debt
        static::updated(function ($debt) {
            $changes = $debt->getChanges();
            if (!empty($changes)) {
                self::logDebtActivity(
                    $debt->id,
                    DebtActivityLog::ACTION_UPDATE,
                    self::getCurrentActor(),
                    'Cập nhật thông tin công nợ',
                    $debt->getOriginal(),
                    $debt->toArray()
                );
            }
        });

        // Log khi xóa debt
        static::deleted(function ($debt) {
            self::logDebtActivity(
                $debt->id,
                DebtActivityLog::ACTION_DELETE,
                self::getCurrentActor(),
                'Xóa công nợ',
                $debt->toArray(),
                null
            );
        });
    }

    /**
     * Log debt activity manually
     */
    public function logActivity($action, $description = null, $oldData = null, $newData = null)
    {
        return self::logDebtActivity(
            $this->id,
            $action,
            self::getCurrentActor(),
            $description,
            $oldData ?: $this->getOriginal(),
            $newData ?: $this->toArray()
        );
    }

    /**
     * Log debt cancellation
     */
    public function logCancellation($reason = null)
    {
        return $this->logActivity(
            DebtActivityLog::ACTION_CANCEL,
            $reason ? "Hủy công nợ: {$reason}" : 'Hủy công nợ'
        );
    }

    /**
     * Log debt payment
     */
    public function logPayment($amount, $paymentMethod = null)
    {
        $description = "Thanh toán công nợ: " . number_format($amount, 0, ',', '.') . ' VNĐ';
        if ($paymentMethod) {
            $description .= " (Phương thức: {$paymentMethod})";
        }

        return $this->logActivity(
            DebtActivityLog::ACTION_PAYMENT,
            $description
        );
    }

    /**
     * Log debt restoration
     */
    public function logRestoration($reason = null)
    {
        return $this->logActivity(
            DebtActivityLog::ACTION_RESTORE,
            $reason ? "Khôi phục công nợ: {$reason}" : 'Khôi phục công nợ'
        );
    }

    /**
     * Get activity logs for this debt
     */
    public function activityLogs()
    {
        return $this->hasMany(DebtActivityLog::class, 'debt_id');
    }

    /**
     * Get recent activity logs
     */
    public function recentActivityLogs($limit = 10)
    {
        return $this->activityLogs()
                    ->orderBy('created_at', 'desc')
                    ->limit($limit);
    }

    /**
     * Static method to log debt activity
     */
    protected static function logDebtActivity($debtId, $action, $actor, $description = null, $oldData = null, $newData = null)
    {
        try {
            return DebtActivityLog::createLog(
                $debtId,
                $action,
                $actor,
                $description,
                $oldData,
                $newData
            );
        } catch (\Exception $e) {
            // Log error but don't fail the main operation
            \Log::error('Failed to log debt activity', [
                'debt_id' => $debtId,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get current actor (user performing the action)
     */
    protected static function getCurrentActor()
    {
        if (Auth::check()) {
            return Auth::user()->user_login ?? Auth::user()->email ?? 'authenticated_user';
        }

        // Check if running in console/queue
        if (app()->runningInConsole()) {
            return 'system';
        }

        return 'anonymous';
    }
}
