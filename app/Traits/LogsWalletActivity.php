<?php

namespace App\Traits;

use App\Models\Fee\WalletActivityLog;
use Illuminate\Support\Facades\Auth;

trait LogsWalletActivity
{
    /**
     * Boot the trait
     */
    protected static function bootLogsWalletActivity()
    {
        // Log khi tạo wallet mới
        static::created(function ($wallet) {
            self::logWalletActivity(
                $wallet->id,
                $wallet->user_code,
                WalletActivityLog::ACTION_CREATE,
                self::getCurrentActor(),
                'Tạo ví mới',
                null,
                $wallet->toArray()
            );
        });

        // Log khi cập nhật wallet
        static::updated(function ($wallet) {
            $changes = $wallet->getChanges();
            if (!empty($changes)) {
                $action = WalletActivityLog::ACTION_CHANGE_INFO;
                $description = 'Cập nhật thông tin ví';
                
                // Xác định loại thay đổi cụ thể
                if (isset($changes['balance'])) {
                    $action = WalletActivityLog::ACTION_UPDATE_BALANCE;
                    $description = 'Cập nhật số dư ví';
                }
                
                if (isset($changes['is_locked'])) {
                    $action = $changes['is_locked'] ? WalletActivityLog::ACTION_LOCK : WalletActivityLog::ACTION_UNLOCK;
                    $description = $changes['is_locked'] ? 'Khóa ví' : 'Mở khóa ví';
                }

                self::logWalletActivity(
                    $wallet->id,
                    $wallet->user_code,
                    $action,
                    self::getCurrentActor(),
                    $description,
                    $wallet->getOriginal(),
                    $wallet->toArray()
                );
            }
        });
    }

    /**
     * Log wallet activity manually
     */
    public function logActivity($action, $description = null, $oldData = null, $newData = null, $amountInvolved = null, $transactionRef = null)
    {
        return self::logWalletActivity(
            $this->id,
            $this->user_code,
            $action,
            self::getCurrentActor(),
            $description,
            $oldData ?: $this->getOriginal(),
            $newData ?: $this->toArray(),
            $amountInvolved,
            $transactionRef
        );
    }

    /**
     * Log wallet lock
     */
    public function logLock($reason = null)
    {
        return $this->logActivity(
            WalletActivityLog::ACTION_LOCK,
            $reason ? "Khóa ví: {$reason}" : 'Khóa ví'
        );
    }

    /**
     * Log wallet unlock
     */
    public function logUnlock($reason = null)
    {
        return $this->logActivity(
            WalletActivityLog::ACTION_UNLOCK,
            $reason ? "Mở khóa ví: {$reason}" : 'Mở khóa ví'
        );
    }

    /**
     * Log deposit
     */
    public function logDeposit($amount, $description = null, $transactionRef = null)
    {
        $desc = $description ?: "Nạp tiền vào ví: " . number_format($amount, 0, ',', '.') . ' VNĐ';
        
        return $this->logActivity(
            WalletActivityLog::ACTION_DEPOSIT,
            $desc,
            null,
            null,
            $amount,
            $transactionRef
        );
    }

    /**
     * Log withdrawal
     */
    public function logWithdraw($amount, $description = null, $transactionRef = null)
    {
        $desc = $description ?: "Rút tiền từ ví: " . number_format($amount, 0, ',', '.') . ' VNĐ';
        
        return $this->logActivity(
            WalletActivityLog::ACTION_WITHDRAW,
            $desc,
            null,
            null,
            $amount,
            $transactionRef
        );
    }

    /**
     * Log transfer
     */
    public function logTransfer($amount, $toWallet, $description = null, $transactionRef = null)
    {
        $desc = $description ?: "Chuyển tiền: " . number_format($amount, 0, ',', '.') . " VNĐ đến ví {$toWallet}";
        
        return $this->logActivity(
            WalletActivityLog::ACTION_TRANSFER,
            $desc,
            null,
            null,
            $amount,
            $transactionRef
        );
    }

    /**
     * Get activity logs for this wallet
     */
    public function activityLogs()
    {
        return $this->hasMany(WalletActivityLog::class, 'wallet_id');
    }

    /**
     * Get recent activity logs
     */
    public function recentActivityLogs($limit = 10)
    {
        return $this->activityLogs()
                    ->orderBy('created_at', 'desc')
                    ->limit($limit);
    }

    /**
     * Static method to log wallet activity
     */
    protected static function logWalletActivity($walletId, $userCode, $action, $actor, $description = null, $oldData = null, $newData = null, $amountInvolved = null, $transactionRef = null)
    {
        try {
            return WalletActivityLog::createLog(
                $walletId,
                $userCode,
                $action,
                $actor,
                $description,
                $oldData,
                $newData,
                $amountInvolved,
                $transactionRef
            );
        } catch (\Exception $e) {
            // Log error but don't fail the main operation
            \Log::error('Failed to log wallet activity', [
                'wallet_id' => $walletId,
                'user_code' => $userCode,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get current actor (user performing the action)
     */
    protected static function getCurrentActor()
    {
        if (Auth::check()) {
            return Auth::user()->user_login ?? Auth::user()->email ?? 'authenticated_user';
        }

        // Check if running in console/queue
        if (app()->runningInConsole()) {
            return 'system';
        }

        return 'anonymous';
    }
}
