# Tối ưu hóa hoàn chỉnh hệ thống Debt - Bỏ bảng fee_details

## Tóm tắt thay đổi

Theo đề xuất củ<PERSON> bạn, chúng ta đã **BỎ HOÀN TOÀN bảng `fee_details`** vì không cần thiết và thay thế bằng việc bổ sung các trường vào `student_debts`.

## 1. **C<PERSON>u trúc Database cuối cùng**

### Bảng `student_debts` (đã tối ưu):
```sql
-- Thông tin cơ bản
id, user_code, user_login, term_id, term_name, fee_type_id

-- Thông tin từ fee_details (MỚI)
fee_id BIGINT NULL -- ID phí chính (nếu có)
ki_thu INT DEFAULT 1 -- <PERSON><PERSON> thứ
type_fee TINYINT -- Loại phí chi tiết (1=<PERSON><PERSON><PERSON> <PERSON><PERSON>, 2=<PERSON><PERSON><PERSON>, 3-4=Tiếng anh, 5=<PERSON><PERSON> sung)
version INT DEFAULT 1 -- Phiên bản

-- Thông tin discount (MỚI)
original_amount DECIMAL(15,2) -- <PERSON>ố tiền gốc (trước discount)
discount_amount DECIMAL(15,2) DEFAULT 0 -- Số tiền được giảm
discount_percentage DECIMAL(5,2) DEFAULT 0 -- % giảm (0-100)
discount_reason VARCHAR(255) -- Lý do giảm giá

-- Số tiền cuối cùng
amount DECIMAL(15,2) -- Số tiền phải trả (sau discount)
paid_amount DECIMAL(15,2) DEFAULT 0 -- Đã trả

-- Trạng thái và audit
status, description, created_by, canceled_by, canceled_at, created_at, updated_at
```

### ❌ **Bảng `fee_details` - ĐÃ BỎ HOÀN TOÀN**

## 2. **Migration thực hiện**

### File: `2025_06_20_000006_remove_redundant_fee_details.php`

**Các bước thực hiện:**
1. **Bổ sung trường** từ `fee_details` vào `student_debts`
2. **Migrate dữ liệu** từ `fee_details` sang `student_debts`
3. **Set default values** cho records không có fee_details
4. **Xóa bảng `fee_details`**

```sql
-- Thêm trường vào student_debts
ALTER TABLE student_debts ADD COLUMN fee_id BIGINT NULL;
ALTER TABLE student_debts ADD COLUMN ki_thu INT DEFAULT 1;
ALTER TABLE student_debts ADD COLUMN type_fee TINYINT;
ALTER TABLE student_debts ADD COLUMN version INT DEFAULT 1;

-- Migrate dữ liệu
UPDATE student_debts SET 
    fee_id = (SELECT fee_id FROM fee_details WHERE debt_id = student_debts.id),
    type_fee = (SELECT type_fee FROM fee_details WHERE debt_id = student_debts.id),
    ki_thu = (SELECT ki_thu FROM fee_details WHERE debt_id = student_debts.id),
    version = (SELECT version FROM fee_details WHERE debt_id = student_debts.id);

-- Set default cho records không có fee_details
UPDATE student_debts SET 
    type_fee = CASE fee_type_id
        WHEN 1 THEN 1  -- Học phí -> Phí học kỳ
        WHEN 2 THEN 5  -- Phí ký túc xá -> Bổ sung
        WHEN 3 THEN 5  -- Phí bảo hiểm -> Bổ sung
        WHEN 4 THEN 1  -- Phí học lại -> Phí học kỳ
        WHEN 5 THEN 1  -- Phí thi lại -> Phí học kỳ
        WHEN 6 THEN 5  -- Phí khác -> Bổ sung
        ELSE 5
    END,
    ki_thu = 1,
    version = 1
WHERE type_fee IS NULL;

-- Xóa bảng fee_details
DROP TABLE fee_details;
```

## 3. **Code đã cập nhật**

### **Model StudentDebt:**
```php
protected $fillable = [
    'user_code', 'user_login', 'term_id', 'term_name', 'fee_type_id',
    // Từ fee_details
    'fee_id', 'ki_thu', 'type_fee', 'version',
    // Discount
    'original_amount', 'discount_amount', 'discount_percentage', 'discount_reason',
    // Số tiền
    'amount', 'paid_amount',
    // Trạng thái
    'status', 'description', 'created_by', 'canceled_by', 'canceled_at'
];

// Method mới
public function getTypeFeeNameAttribute() {
    $typeNames = [
        1 => 'Phí học kỳ',
        2 => 'Phí sách', 
        3 => 'Tiếng anh cơ bản',
        4 => 'Tiếng anh nâng cao',
        5 => 'Bổ sung'
    ];
    return $typeNames[$this->type_fee] ?? 'Không xác định';
}
```

### **Repository:**
```php
// Không cần tạo FeeDetail nữa
$debt = StudentDebt::create([
    'user_code' => $data['user_code'],
    'user_login' => $data['user_login'],
    'term_id' => $data['term_id'],
    'term_name' => $data['term']->term_name,
    'fee_type_id' => $data['fee_type_id'],
    // Trường từ fee_details
    'fee_id' => $data['fee_id'] ?? null,
    'ki_thu' => $this->getKiThuFromTerm($data['term_id']),
    'type_fee' => $typeFee,
    'version' => 1,
    // Discount
    'original_amount' => $originalAmount,
    'discount_amount' => $discountAmount,
    'discount_percentage' => $discountPercentage,
    'discount_reason' => $data['discount_reason'] ?? null,
    // Số tiền cuối
    'amount' => $finalAmount,
    'paid_amount' => 0,
    'status' => self::DEBT_STATUS_PENDING,
    'description' => $data['description'],
    'created_by' => Auth::user()->user_login ?? 'system'
]);

// Không cần createFeeDetailForDebt() nữa
```

### **Controller:**
```php
// Query với các trường mới
$query = Debt::query()
    ->select(
        'student_debts.*', 
        'term.term_name', 
        'fee_types.name as fee_type_name',
        // Các trường mới
        'student_debts.type_fee',
        'student_debts.ki_thu', 
        'student_debts.version',
        'student_debts.original_amount',
        'student_debts.discount_amount',
        'student_debts.discount_percentage',
        'student_debts.discount_reason'
    )
    ->leftJoin('term', 'student_debts.term_id', '=', 'term.id')
    ->leftJoin('fee_types', 'student_debts.fee_type_id', '=', 'fee_types.id');
```

### **Import:**
```php
// Thêm trường vào debtData
$debtData['fee_id'] = $cleanData['fee_id'] ?? null;
$debtData['ki_thu'] = $this->getKiThuFromTerm($cleanData['term_id']);
$debtData['type_fee'] = $typeFee;
$debtData['version'] = 1;

$debt = StudentDebt::create($debtData);
// Không cần createFeeDetailForDebt() nữa
```

## 4. **Lợi ích đạt được**

### ✅ **Đơn giản hóa:**
- Bỏ 1 bảng không cần thiết
- Giảm complexity
- Ít JOIN queries hơn

### ✅ **Performance:**
- Ít bảng để query
- Ít foreign key constraints
- Faster queries

### ✅ **Maintainability:**
- Ít code để maintain
- Không có data inconsistency giữa 2 bảng
- Single source of truth

### ✅ **Discount chính xác:**
- Validation đầy đủ
- Mặc định discount_percentage = 0
- Tính toán chính xác với làm tròn
- Hỗ trợ Excel import

## 5. **Cách sử dụng mới**

### **Tạo debt:**
```php
$debt = StudentDebt::create([
    'user_code' => 'SV001',
    'original_amount' => 1000000,
    'discount_percentage' => 10,
    'discount_reason' => 'Học sinh giỏi',
    'type_fee' => 1, // Phí học kỳ
    'ki_thu' => 1,
    // ... other fields
]);

// Tự động tính:
// discount_amount = 100000
// amount = 900000 (số tiền phải trả)
```

### **Query debt với thông tin chi tiết:**
```php
$debts = StudentDebt::select(
    'student_debts.*',
    'term.term_name',
    'fee_types.name as fee_type_name'
)
->leftJoin('term', 'student_debts.term_id', '=', 'term.id')
->leftJoin('fee_types', 'student_debts.fee_type_id', '=', 'fee_types.id')
->get();

// Không cần JOIN với fee_details nữa!
```

## 6. **Migration Commands**

```bash
# Chạy migrations
php artisan migrate

# Rollback nếu cần
php artisan migrate:rollback --step=2
```

## Kết luận

Đã hoàn thành việc **BỎ BẢNG `fee_details`** và **tối ưu hóa discount** theo đúng yêu cầu của bạn:

1. ✅ Bỏ bảng `fee_details` thừa
2. ✅ Bổ sung đầy đủ trường vào `student_debts`
3. ✅ Migrate dữ liệu an toàn
4. ✅ Cập nhật toàn bộ code liên quan
5. ✅ Validation discount chặt chẽ
6. ✅ Hỗ trợ Excel import đầy đủ

Hệ thống bây giờ đã tối ưu, đơn giản và hiệu quả hơn nhiều! 🎉
