# Tối ưu hóa hệ thống quản lý công nợ (Debt System)

## Vấn đề đã phát hiện và khắc phục

### 1. **Trường `status` thừa trong bảng `fee_details`**

**Vấn đề:**
- Bảng `fee_details` có trường `status` duplicate với `student_debts.status`
- Gây ra nguy cơ data inconsistency
- Không cần thiết vì có thể JOIN để lấy trạng thái

**Giải pháp:**
- ✅ Bỏ trường `status` khỏi `fee_details`
- ✅ Sử dụng relationship để lấy status từ `student_debts`
- ✅ Thêm accessor `getStatusAttribute()` trong model `FeeDetail`

### 2. **Trường `english_level` không liên quan**

**Vấn đề:**
- Trường `english_level_id` và `english_level` chỉ áp dụng cho phí tiếng anh
- Không phải tất cả `fee_details` đều cần thông tin này
- <PERSON><PERSON><PERSON> phức tạp cấu trúc database

**Giải pháp:**
- ✅ Bỏ các trường `english_level_id`, `english_level` khỏi `fee_details`
- ✅ Nếu cần quản lý English level, tạo bảng riêng hoặc lưu trong `description`

### 3. **Cải thiện performance**

**Vấn đề:**
- Thiếu indexes cho các query thường dùng

**Giải pháp:**
- ✅ Thêm composite indexes:
  - `['user_code', 'term_id']`
  - `['debt_id', 'type_fee']`
  - `['term_id', 'type_fee']`

## Files đã được cập nhật

### 1. Migration tối ưu hóa
```
database/migrations/2025_06_20_000001_optimize_fee_details_table.php
```

### 2. Model FeeDetail
```
app/Models/Fee/FeeDetail.php
```
- Bỏ `status`, `english_level_id`, `english_level` khỏi `$fillable`
- Thêm accessor `getStatusAttribute()` để lấy status từ relationship
- Thêm method `isPaid()` để kiểm tra trạng thái thanh toán

### 3. Repository
```
app/Repositories/Admin/StudentDebtRepository.php
```
- Cập nhật `createFeeDetailForDebt()` để không tạo trường `status`

### 4. Import class
```
app/Imports/DebtBulkImport.php
```
- Cập nhật `createFeeDetailForDebt()` để không tạo trường `status`

## Cấu trúc database sau khi tối ưu

### Bảng `student_debts` (không đổi)
- Quản lý trạng thái chính của công nợ
- Là single source of truth cho status

### Bảng `fee_details` (đã tối ưu)
- Chỉ chứa thông tin chi tiết phí cần thiết
- Lấy status thông qua relationship với `student_debts`
- Bỏ các trường không liên quan

### Bảng `debt_payments` (không đổi)
- Lưu lịch sử thanh toán

## Cách sử dụng sau khi tối ưu

### Lấy trạng thái thanh toán của FeeDetail:
```php
$feeDetail = FeeDetail::with('studentDebt')->find(1);
$status = $feeDetail->status; // Lấy từ accessor
$isPaid = $feeDetail->isPaid(); // Method helper
```

### Query với JOIN để lấy status:
```php
$feeDetails = FeeDetail::select('fee_details.*', 'student_debts.status')
    ->join('student_debts', 'fee_details.debt_id', '=', 'student_debts.id')
    ->where('student_debts.status', 1) // Đã thanh toán
    ->get();
```

## Lợi ích đạt được

1. **Tính nhất quán dữ liệu**: Chỉ có 1 nơi lưu trạng thái thanh toán
2. **Đơn giản hóa cấu trúc**: Bỏ các trường không cần thiết
3. **Tăng performance**: Thêm indexes phù hợp
4. **Dễ bảo trì**: Logic rõ ràng, không duplicate data

## Chạy migration

```bash
php artisan migrate
```

Migration sẽ:
- Bỏ trường `status`, `english_level_id`, `english_level` khỏi `fee_details`
- Thêm các indexes cần thiết
- Có thể rollback nếu cần
