<?php

use App\Http\Controllers\Admin\ActivityController;
use App\Http\Controllers\Admin\AdministrativeClassController;
use App\Http\Controllers\Admin\DepartmentController;
use App\Http\Controllers\Admin\MajorController;
use App\Http\Controllers\Api\ProfileController;
use App\Http\Controllers\Api\OtherManageController;
use App\Http\Controllers\Api\UserApiController;
use App\Http\Controllers\Admin\EducateController;
use App\Http\Controllers\Api\DngConnectController;
use App\Http\Controllers\Api\TermController;
use App\Http\Controllers\Api\BlockController;
use App\Http\Controllers\Admin\NewFeedbackController;
use App\Http\Controllers\Admin\FeeController;
use App\Http\Controllers\Admin\PopupController;
use App\Http\Controllers\Api\GradeController;
use App\Http\Controllers\Api\PluginController;
use App\Http\Controllers\Api\StudentHelperController;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\OtpController;
use App\Http\Controllers\Api\StudentSmsController;
use App\Http\Controllers\Teacher\AttendanceController;
use App\Http\Controllers\Admin\PromotedSemesterController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\ScanStudentController;
use App\Http\Controllers\Admin\ScheduleController;
use App\Http\Controllers\Api\RoomController;
use App\Http\Controllers\Api\RemoveStudentController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\ScanningStudentController;
use App\Http\Controllers\Admin\SubjectController;
use App\Http\Controllers\Admin\SmsByTemplateController;
use App\Http\Controllers\Admin\SmsRoleController;
use App\Http\Controllers\Admin\StudentWarningController;
use App\Http\Controllers\Admin\BhytController;
use App\Http\Controllers\Admin\CurriculumController;
use App\Http\Controllers\Admin\SyllabusController;
use App\Http\Controllers\Api\SubjectInfoController;
use App\Http\Controllers\Admin\EBookController;
use App\Http\Controllers\Admin\FeeSubjectController;
use App\Http\Controllers\Admin\LectureLessonController;
use App\Http\Controllers\Admin\LectureTeacherController;
use App\Http\Controllers\Admin\NewsletterStudentController;
use App\Http\Controllers\Admin\SessionTypeController;
use App\Http\Controllers\Admin\StudentStatusLogsController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\TermController as AdminTermController;
use App\Http\Controllers\Admin\AdmissionController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/


Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});
Route::get('/otp-send', [OtpController::class, 'sendOtp']);
Route::get('/otp-valid', [OtpController::class, 'validOtp']);

Route::get('/student/detail/sms', [StudentSmsController::class, 'sms']);
Route::post('/student/detail/sms/active', [StudentSmsController::class, 'activePhone']);
Route::post('/student/detail/sms/deactive', [StudentSmsController::class, 'deactivePhone']);

Route::prefix('profile')->group(function () {
    Route::prefix('sms')->group(function () {
        Route::get('/getListPhoneNumber', [StudentSmsController::class, 'getListPhoneNumber']);
        Route::post('/changePhoneNumber', [StudentSmsController::class, 'changePhoneNumber']);
        Route::post('/changeStatusPhoneNumber', [StudentSmsController::class, 'changeStatusPhoneNumber']);
        Route::post('/saveNewPhoneNumber', [StudentSmsController::class, 'saveNewPhoneNumber']);
    });
});

Route::group(['middleware' => ['apiSecure', 'api']], function () {

    Route::post('/upload-file', [PopupController::class, 'upload_file']);
    Route::get('/getListCheckPaymentDNG', [FeeController::class, 'getListCheckPaymentDNG']);
    Route::get('/exportExcelCheckPaymentDng', [FeeController::class, 'exportExcelCheckPaymentDng']);
    Route::post('/postUpdateDngVAT', [FeeController::class, 'postUpdateDngVAT']);
    Route::post('/postUpdateDngNote', [FeeController::class, 'postUpdateDngNote']);
    Route::post('/updateVATByExcel', [FeeController::class, 'updateVATByExcel']);
    Route::get('/getFullInfoDng', [FeeController::class, 'getFullInfoDng']);
    Route::get('/checkDngByDate', [FeeController::class, 'checkDngByDate']);
    Route::get('/checkPaymentDngByItemID', [FeeController::class, 'checkPaymentDngByItemID']);
    Route::get('/getListStudentStatusLogs', [StudentStatusLogsController::class, 'getListStudentStatusLogs']);
    Route::get('/getListLectureLesson', [LectureLessonController::class, 'getListLectureLesson']);
    Route::get('/getListLectureTeacher', [LectureTeacherController::class, 'getListLectureTeacher']);
    Route::get('/getListNewsletterStudent', [NewsletterStudentController::class, 'getListNewsletterStudent']);
    Route::get('/getListStatusStudent3Ben', [UserController::class, 'getListStatusStudent3Ben']);


    Route::prefix('QnA')->group(function () {
        Route::get('/getQnA', [StudentHelperController::class, 'getQnA']);
        Route::get('/getCategory', [StudentHelperController::class, 'getCategory']);
        Route::post('/createCategory', [StudentHelperController::class, 'createCategory']);
        Route::delete('/deleteCategory/{id}', [StudentHelperController::class, 'deleteCategory']);
        Route::put('/editCategory/{id}', [StudentHelperController::class, 'editCategory']);
        Route::post('/createQnA', [StudentHelperController::class, 'createQnA']);
        Route::delete('/deleteQnA/{id}', [StudentHelperController::class, 'deleteQnA']);
        Route::put('/editQnA/{id}', [StudentHelperController::class, 'editQnA']);
    });

    Route::prefix('other_manage')->name('other_manage.')->group(function () {
        Route::get('/getListArea', [OtherManageController::class, 'getListArea'])->name('getListArea');
        Route::post('/createdArea', [OtherManageController::class, 'createdArea'])->name('createdArea');
        Route::get('/getListRoom', [OtherManageController::class, 'getListRoom'])->name('getListRoom');
        Route::post('/createdRoom', [OtherManageController::class, 'createdRoom'])->name('createdRoom');
        Route::put('/editRoom/{id}', [OtherManageController::class, 'editRoom'])->name('editRoom');
        Route::delete('/deleteRoom/{id}', [OtherManageController::class, 'deleteRoom'])->name('deleteRoom');
        Route::get('/getListTerm', [OtherManageController::class, 'getListTerm'])->name('getListTerm');
        Route::get('/checkUserPermission', [OtherManageController::class, 'checkUserPermission'])->name('checkUserPermission');
        Route::post('/createdBlock', [OtherManageController::class, 'createdBLock'])->name('createdBlock');
        Route::put('/editBlock/{id}', [OtherManageController::class, 'editBlock'])->name('editBlock');
        Route::delete('/deleteBlock/{id}', [OtherManageController::class, 'deleteBlock'])->name('deleteBlock');
        Route::prefix('slot')->name('slot.')->group(function () {
            Route::get('/list', [OtherManageController::class, 'getListSlot'])->name('list');
            Route::put('/edit/{id}', [OtherManageController::class, 'editSlot'])->name('edit');
            Route::post('/create', [OtherManageController::class, 'createSlot'])->name('create');
            Route::delete('/delete/{id}', [OtherManageController::class, 'deleteSlot'])->name('delete');
        });
        Route::prefix('khoa_nhap_hoc')->name('khoa_nhap_hoc.')->group(function () {
            Route::get('/list', [OtherManageController::class, 'getListKhoaNhapHoc'])->name('list');
            Route::post('/create', [OtherManageController::class, 'createKhoaNhapHoc'])->name('create');
            Route::post('/delete', [OtherManageController::class, 'deleteKhoaNhapHoc'])->name('delete');
        });
        Route::get('/teacher/list', [OtherManageController::class, 'getListTeacher'])->name('teacher.list');
    });

    Route::prefix('gradebook')->group(function () {
        Route::get('/getInfoExportGradeBook', [GradeController::class, 'getInfoExportGradeBook']);
        Route::get('/getExportGradeBook', [GradeController::class, 'getExportGradeBook']);
        Route::get('/getListExistedFileGradebook', [GradeController::class, 'getListExistedFileGradebook']);
        Route::get('/getDownloadExistedFileGradebook', [GradeController::class, 'getDownloadExistedFileGradebook']);
    });

    Route::prefix('thieuNoMon')->group(function () {
        Route::get('/getInfoExportThieuNoMon', [EducateController::class, 'getInfoExportThieuNoMon'])->name('getInfo');
        // Route::get('/getExportThieuNoMon', [GradeController::class, 'getExportThieuNoMon'])->name('getExport');
        Route::get('/getListExistedFileThieuNoMon', [EducateController::class, 'getListExistedFileThieuNoMon'])->name('getList');
        Route::get('/getDownloadExistedFileThieuNoMon', [EducateController::class, 'getDownloadExistedFileThieuNoMon'])->name('getDownload');
    });


    Route::prefix('plugin')->group(function () {
        Route::post('/uploadFileToSource', [PluginController::class, 'uploadFileToSource']);
        Route::delete('/deleteFileFromSource', [PluginController::class, 'deleteFileFromSource']);
    });
    Route::get('/test-schedule', [ActivityController::class, 'testSchedule']);

    Route::prefix('check-schedule')->group(function () {
        Route::get('/getCheckRoomByDate', [RoomController::class, 'getCheckRoomByDate']);
        Route::post('/postRegisterRoom', [RoomController::class, 'postRegisterRoom']);
        Route::delete('/deleteBookingRoom', [RoomController::class, 'deleteBookingRoom']);
        Route::put('/putUpdateBookingRoom', [RoomController::class, 'putUpdateBookingRoom']);
    });

    Route::prefix('scan_student')->group(function () {
        Route::get('/getListScanStudent', [ScanStudentController::class, 'getListScanStudent']);
    });

    Route::prefix('fee')->name('fee.')->group(function () {
        Route::post('/scan-fee', [FeeController::class, 'scanFee'])->name('scan_fee');
    });

    Route::prefix('remove_student')->group(function () {
        Route::get('/getListStudentRemove_30', [RemoveStudentController::class, 'getListStudentRemove_30']);
        Route::post('/createListStudentRemove_30', [RemoveStudentController::class, 'createListStudentRemove_30']);
        Route::delete('/deleteStudentRemove_30', [RemoveStudentController::class, 'deleteStudentRemove_30']);
        Route::delete('/deleteListStudentRemove_30', [RemoveStudentController::class, 'deleteListStudentRemove_30']);
        Route::get('/getListStudentRemoveAp', [RemoveStudentController::class, 'getListStudentRemoveAp']);
        Route::post('/createListStudentRemoveAp', [RemoveStudentController::class, 'createListStudentRemoveAp']);
        Route::delete('/deleteStudentRemoveAp', [RemoveStudentController::class, 'deleteStudentRemoveAp']);
        Route::delete('/deleteListStudentRemoveAp', [RemoveStudentController::class, 'deleteListStudentRemoveAp']);
    });

    Route::prefix('scanning_student')->group(function () {
        Route::post('/importFee', [ScanningStudentController::class, 'importFee'])->name('importFee');
        Route::get('/getDataImportFee', [ScanningStudentController::class, 'getDataImportFee'])->name('getDataImportFee');
        Route::get('/getListScanningStudent', [ScanningStudentController::class, 'getListScanningStudent']);
    });

    Route::prefix('term')->name('term.')->group(function () {
        Route::get('/get-data-by-option', [TermController::class, 'getDatasByOption'])->name('get_data_by_option');
    });


    Route::prefix('block')->name('block.')->group(function () {
        Route::get('/get-data-by-option', [BlockController::class, 'getDatasByOption'])->name('get_data_by_option');
    });

    Route::prefix('user')->name('user.')->group(function () {
        Route::get('/get-data-by-option', [UserApiController::class, 'getDatasByOption'])->name('get_data_by_option');
        Route::post('/update-info-from-crm', [UserApiController::class, 'updateInforUserFromCrm'])->name('update_info_from_crm');
    });

    Route::prefix('subject')->name('subject.')->group(function () {
        Route::get('/get-list', [SubjectController::class, 'getListSubject'])->name('get_data_by_option');
        Route::get('/download', [SubjectController::class, 'downloadSubject']);
        // Route::get('/get-data-by-option',[UserApiController::class, 'getDatasByOption'])->name('get_data_by_option');
        // Route::get('/get-data-by-option',[UserApiController::class, 'getDatasByOption'])->name('get_data_by_option');
    });

    Route::prefix('feedback')->name('feedback.')->group(function () {
        Route::post('/update-config-day', [NewFeedbackController::class, 'updateConfigTimeFeedback'])->name('updateConfigDay');
        Route::post('/change-status', [NewFeedbackController::class, 'changeStatus'])->name('changeStatus');
        Route::post('/sync-data', [NewFeedbackController::class, 'syncData'])->name('syncData');
        Route::post('/open-feedback', [NewFeedbackController::class, 'openFeedback'])->name('openFeedback');
        Route::post('/open-feedback-by-activity', [NewFeedbackController::class, 'openFeedbackByActivity'])->name('openFeedbackByActivity');
        Route::post('/sync-feedback', [NewFeedbackController::class, 'syncFeedback'])->name('syncFeedback');
        Route::post('/set-planer-user', [NewFeedbackController::class, 'setPlanerUser'])->name('setPlanerUser');
    });

    Route::prefix('fee_mail')->name('fee_mail.')->group(function () {
        Route::get('/get-data-by-option', [UserApiController::class, 'getDatasByOption'])->name('get_data_by_option');
    });

    Route::post('/advance_search', 'Api\SearchController@advanceSearch')->name('search');
    Route::get('/terms', 'Api\TermController@getAll')->name('terms');
    Route::post('/mac_address/store', 'Admin\HomeController@storeMac')->name('test');
    Route::get('/schedules', [ProfileController::class, 'lichHoc'])->name('schedules');
    Route::get('/attendances', [ProfileController::class, 'diemDanh'])->name('attendances');
    Route::get('/studying', [ProfileController::class, 'cacMonDangHoc'])->name('studying');
    Route::get('/transfer_result', [ProfileController::class, 'lichSuHocChuyenCoSo'])->name('transfer_result');
    Route::get('/grade_point', [ProfileController::class, 'bangDiem'])->name('grade_point');
    Route::get('/discipline', [ProfileController::class, 'kyLuatKhenThuong'])->name('discipline');
    Route::get('/sms', [ProfileController::class, 'sms'])->name('sms');
    Route::get('/history', [ProfileController::class, 'histories'])->name('history');
    Route::get('/grade_point_detail', [ProfileController::class, 'bangDiemChiTiet'])->name('grade_point_detail');
    Route::post('/change_attendance', [AttendanceController::class, 'updateAttendanceFromCadres'])->name('change_attendance');

    Route::middleware('check_ip')->post('/sync/data/basic', 'Api\UserApiController@syncDataBasic')->name('sync_data_basic');
    Route::middleware('check_ip')->post('/email/wallet/update', 'Api\UserApiController@sendEmailWhenUpdateWallet')->name('email.wallet.update');
    Route::get('/attendance-management/select-data', [AttendanceController::class, 'selectDataListAttendance']);
    Route::get('/attendance-management', [AttendanceController::class, 'listAttendance']);
    Route::get('/attendance-management/export', [AttendanceController::class, 'export']);
    Route::prefix('lich-trinh')->group(function () {
        Route::get('/khung-chuong-trinh', [ScheduleController::class, 'CurriculumList']);
        Route::get('/giai-doan', [ScheduleController::class, 'curriculumPeriod']);
        Route::get('/sinh-vien', [ScheduleController::class, 'curriculumStudent']);
        Route::get('/khung-chuong-trinh/chi-tiet/{id}', [ScheduleController::class, 'curriculumDetail']);
        Route::get('/khung-chuong-trinh/chuyen-nganh/{brand_code}', [ScheduleController::class, 'listChuyenNganh']);
        Route::post('/khung-chuong-trinh/create', [ScheduleController::class, 'curriculumAdd']);
        Route::put('/khung-chuong-trinh/edit/{id}', [ScheduleController::class, 'curriculumEdit']);
        Route::get('/mon-tu-chon/list', [ScheduleController::class, 'getlistSubject']);
        Route::get('/mon-tu-chon-theo-curriculum', [ScheduleController::class, 'getListSubjectInCurriculum']);
        Route::post('/mon-tu-chon/create', [ScheduleController::class, 'electiveGroupAdd']);
        Route::put('/mon-tu-chon/update/{id}', [ScheduleController::class, 'electiveGroupEdit']);
        Route::get('/danh-sach-hoc-phan-tu-chon/{curriculum}', [ScheduleController::class, 'getElectiveGroupsInCurriculum']);
        Route::post('/luu-hoc-phan-tu-chon', [ScheduleController::class, 'updateElectiveSubjects']);
        Route::get('/list-mon-tu-chon', [ScheduleController::class, 'getElectiveSubject']);
        Route::post('period', [ScheduleController::class, 'PeriodAdd']);
        Route::post('subject-period', [ScheduleController::class, 'SubjectPeriodAdd']);
        Route::delete('subject-period/delete', [ScheduleController::class, 'SubjectPeriodDelete']);
        Route::post('/hoc-phan-tu-chon/update', [ScheduleController::class, 'updateElectiveGroup']);
        Route::get('/hoc-phan-tu-chon/delete', [ScheduleController::class, 'deleteElectiveGroup']);
        Route::delete('/khung-chuong-trinh/delete/{id}', [ScheduleController::class, 'deleteCurriculum']);
    });

    Route::get('/department-list', [AttendanceController::class, 'departmentList']);
    Route::get('/brand/get-list', [CurriculumController::class, 'getlistBrand']);
    Route::get('/curriculums/download', [CurriculumController::class, 'downloadCurriculum']);
    Route::get('/brand-list', [AttendanceController::class, 'brandList']);
    Route::get('/list-curiculumList', [UserApiController::class, 'getCuriculumList']);
    Route::post('/submit-student-transfer', [UserApiController::class, 'submitTransferStudent']);
    Route::prefix('syllabus')->group(function () {
        Route::get('list-subject', [SubjectController::class, 'SyllabusListSubject']);
        Route::get('get-subject-types', [SubjectController::class, 'getSubjectTypes']);
        Route::post('add-subject', [SubjectController::class, 'syllabusSubjectAdd']);
        Route::post('edit-subject', [SubjectController::class, 'syllabusSubjectEdit']);
        Route::get('list-department', [SubjectController::class, 'SyllabusListDepartment']);
        Route::get('subjects-for-graduation', [SubjectController::class, 'getSubjectsForGraduation']);
        Route::post('import-subjects-for-graduation', [SubjectController::class, 'importSubjectsForGraduation']);
        Route::delete('subjects-for-graduation/{id}', [SubjectController::class, 'deleteSubjectsForGraduation']);
        Route::get('subject-detail/{id}', [SubjectController::class, 'syllabusSubjectDetail']);
        Route::get('plan-detail/{subject_id}/{syllabus_id}', [SubjectController::class, 'syllabusPlanDetai']);
        Route::get('class-list/{subject_id}/{term_id}', [SubjectController::class, 'syllabusClass']);
        Route::post('subject-en-edit-by-file', [SubjectController::class, 'handleSubmitImportSubjectEng']);
        Route::post('subject-credit-edit-by-file', [SubjectController::class, 'handleSubmitImportSubjectCredits']);
        Route::post('create-syllabus', [SubjectController::class, 'handleCreateSyllabus']);
        Route::post('update-syllabus', [SubjectController::class, 'handleUpdateSyllabus']);
        Route::post('delete-syllabus', [SubjectController::class, 'handleDeleteSyllabus']);
        Route::post('import-syllabus-plan-by-file', [SubjectController::class, 'ImportPlanByFileExcel']);
        Route::post('update-grade-group', [SubjectController::class, 'handleEditGradeGroup']);
        Route::post('update-grades', [SubjectController::class, 'handleEditGrades']);
        Route::post('update-syllabus-plan', [SubjectController::class, 'handleEditSyllabusPlan']);
        Route::get('list-syllabus', [SyllabusController::class, 'getListSyllabus'])->name('syllabus.list_syllabus');
        Route::get('get-list-session-type', [SessionTypeController::class, 'getAllSessionTypes']);
        Route::post('update-session-type', [SessionTypeController::class, 'editSessionType']);
        Route::delete('delete-session-type/{id}', [SessionTypeController::class, 'deleteSessionType']);
        Route::post('create-session-type', [SessionTypeController::class, 'createSessionType']);
    });

    Route::prefix('subject-replace')->group(function () {
        route::get('list', [SubjectController::class, 'SubjectReplaceList']);
        route::post('add', [SubjectController::class, 'SubjectReplaceAdd']);
        route::delete('delete', [SubjectController::class, 'SubjectReplaceDelete']);
    });

    Route::prefix('subject-exempted')->group(function () {
        route::get('list', [subjectController::class, 'getSubjectExempted']);
        route::delete('delete', [subjectController::class, 'deleteSubjectExempted']);
        route::post('import', [SubjectController::class, 'importSubjectExempted']);
    });

    Route::prefix('training-frame')->name('training_frame.')->group(function () {
        route::get('list-student', [ScheduleController::class, 'listStudentInCurriculum']);
        route::post('update-curriculum-student-by-file', [ScheduleController::class, 'updateCurriculumStudent']);
    });

    Route::prefix('change-subject-student')->group(function () {
        route::get('list-student', [SubjectController::class, 'getChangeSubjectStudent']);
        route::post('add', [SubjectController::class, 'ChangeSubjectStudentAdd']);
        route::post('import', [SubjectController::class, 'ChangeSubjectStudentImport']);
        route::delete('delete', [SubjectController::class, 'deleteAlternativeSubject']);
    });

    Route::prefix('role')->name('role.')->group(function () {
        Route::post('/check-permission', [RoleController::class, 'checkPermission'])->name('check_permission');
    });

    Route::prefix('term')->name('term.')->group(function () {
        Route::get('/get-data-by-option', [TermController::class, 'getDatasByOption'])->name('get_data_by_option');
        Route::get('/admin/term-list', [AdminTermController::class, 'termlist']);
        Route::get('/admin/term-create', [AdminTermController::class, 'create']);
        Route::put('/admin/term-update/{term}', [AdminTermController::class, 'update']);
        Route::delete('/admin/term-delete/{id}', [AdminTermController::class, 'delete']);
        Route::put('/admin/term-block/{id}', [AdminTermController::class, 'blockUpdate']);
        Route::get('/admin/term-detail/{term}', [AdminTermController::class, 'termDetail']);
    });

    Route::prefix('sms')->group(function () {
        Route::get('/getSmsCategory', [SmsByTemplateController::class, 'getSmsCategory']);
        Route::get('/getSmsFormat', [SmsByTemplateController::class, 'getSmsFormat']);
        Route::post('/getFormatList', [SmsByTemplateController::class, 'getFormatList']);
        Route::get('/getListTelco', [SmsByTemplateController::class, 'getListTelco']);
        Route::post('/getDataSmsList', [SmsByTemplateController::class, 'getDataSmsList']);
        Route::post('/getSmsListDetail', [SmsByTemplateController::class, 'getSmsListDetail']);
        Route::post('/createFormat', [SmsByTemplateController::class, 'createFormat']);
        Route::post('/importDataSms', [SmsByTemplateController::class, 'importDataSms']);
        Route::post('/setSendTime', [SmsByTemplateController::class, 'setSendTime']);
        Route::post('/sendNow', [SmsByTemplateController::class, 'sendNow']);
        Route::post('/reviewListSms', [SmsByTemplateController::class, 'reviewListSms']);
        Route::post('/acceptListSms', [SmsByTemplateController::class, 'acceptListSms']);
        Route::post('/cancelListSms', [SmsByTemplateController::class, 'cancelListSms']);
        Route::post('/fillDataStudent', [SmsByTemplateController::class, 'fillDataStudent']);
        Route::get('/getPermissionSms', [SmsByTemplateController::class, 'getPermissionSms']);
        Route::get('/getListRole', [SmsByTemplateController::class, 'getListRole']);
        Route::post('/saveFormatWarning', [SmsByTemplateController::class, 'saveFormatWarning']);
        Route::get('/sendSms', [SmsByTemplateController::class, 'sendSms']);
        Route::get('/reSendSms', [SmsByTemplateController::class, 'reSendSms']);
        Route::get('/editSmsList', [SmsByTemplateController::class, 'editSmsList']);
        Route::get('/exportExcelSms', [SmsByTemplateController::class, 'exportExcelSms']);
    });
    Route::prefix('warning')->group(function () {
        Route::get('/getDataWarning', [StudentWarningController::class, 'getDataWarning']);
        Route::get('/getTerm', [StudentWarningController::class, 'getTerm']);
        Route::get('/getBlock', [StudentWarningController::class, 'getBlock']);
        Route::get('/studentWarningUpdate', [StudentWarningController::class, 'studentWarningUpdate']);
        Route::post('/filterStudentWarning', [StudentWarningController::class, 'filterStudentWarning']);
        Route::post('/filterParentWarning', [StudentWarningController::class, 'filterParentWarning']);
        Route::post('/updateStatusSmsWarning', [StudentWarningController::class, 'updateStatusSmsWarning']);
        Route::get('/getFormatSmsWarning', [StudentWarningController::class, 'getFormatSmsWarning']);
        Route::post('/getAllStudentsWarning', [StudentWarningController::class, 'getAllStudentsWarning']);
        Route::get('/deleteStudentWaning', [StudentWarningController::class, 'deleteStudentWaning']);
    });
    Route::prefix('role')->group(function () {
        Route::get('/getRoles', [SmsRoleController::class, 'getRoles']);
        Route::post('/getUserPermissions', [SmsRoleController::class, 'getUserPermissions']);
        Route::post('/saveChangePermissions', [SmsRoleController::class, 'saveChangePermissions']);
    });


    Route::prefix('mon-kien-quyet')->group(function () {
        Route::get('/list', [SubjectController::class, 'getSubjectRequired']);
        Route::post('/duyet', [SubjectController::class, 'ConfirmSubjectRequired']);
        Route::delete('/delete', [SubjectController::class, 'deleteSubjectRequired']);
        Route::post('check-subject', [SubjectController::class, 'checkSubjectExist']);
        Route::post('create', [SubjectController::class, 'createSubjectRequired']);
    });
    Route::prefix('promoted-semester')->group(function () {
        Route::get('/list-promoted-semester_student', [PromotedSemesterController::class, 'getListPromotedSemesterStudent']);
        Route::get('/get-options', [PromotedSemesterController::class, 'getOptions']);
        Route::post('/post-upload-file', [PromotedSemesterController::class, 'uploadImportData']);
        Route::get('/get-export-file', [PromotedSemesterController::class, 'exportPromotedSemesterStudentList']);
    });

    Route::prefix('bhyt-api')->group(function () {
        Route::get('/get-list-available', [BhytController::class, 'getListBhytAvailableRegistrationPeriods']);
        Route::post('/create-update', [BhytController::class, 'postCreateOrEditBhytAvailableRegistrationPeriod']);
        Route::delete('/delete', [BhytController::class, 'deleteBhytAvailableRegistrationPeriod']);
        Route::get('/get-list-order', [BhytController::class, 'getListStudentRegister']);
        Route::get('/get-detail-registration', [BhytController::class, 'getDetailStudentRegister']);
        Route::get('/export-excel-registration', [BhytController::class, 'exportExcel']);
        Route::post('/confirm-payment', [BhytController::class, 'postPaymentConfirmationBhyt']);
        Route::post('/check-payment', [BhytController::class, 'postCheckPayment']);
        Route::post('/postCancelBhyt', [BhytController::class, 'postCancelBhyt']);
    });

    Route::prefix('ebook-api')->group(function () {
        Route::get('/get-list-ebook', [EBookController::class, 'getlistTheEBook']);
        Route::post('/create-sv-ebook', [EBookController::class, 'createSVEBook']);
        Route::post('/update-sv-ebook', [EBookController::class, 'updateSVEBook']);
        Route::delete('/delete-sv-ebook', [EBookController::class, 'deleteSVEBook']);
    });
    Route::prefix('training-frame')->name('training_frame.')->group(function () {
        route::get('list-student', [ScheduleController::class, 'listStudentInCurriculum']);
        route::post('update-curriculum-student-by-file', [ScheduleController::class, 'updateCurriculumStudent']);
    });
    // Quản lý Brand
    Route::prefix('/brand')->name('brand.')->group(function () {
        Route::get('/list', [MajorController::class, 'list'])->name('list');
        Route::post('/create', [MajorController::class, 'store'])->name('store');
        Route::put('/update/{id}', [MajorController::class, 'update'])->name('update');
        Route::delete('/delete/{id}', [MajorController::class, 'delete'])->name('delete');
    });
    // Quản lý Bộ môn
    Route::prefix('/department')->name('department.')->group(function () {
        Route::get('/list', [DepartmentController::class, 'list'])->name('list');
        Route::post('/create', [DepartmentController::class, 'create'])->name('create');
        Route::put('/update/{id}', [DepartmentController::class, 'update'])->name('update');
        Route::delete('/delete/{id}', [DepartmentController::class, 'delete'])->name('delete');
        Route::put('/update-dean/{id}', [DepartmentController::class, 'updateDean'])->name('update-dean');
        Route::put('/update-associate-dean/{id}', [DepartmentController::class, 'updateAssociateDean'])->name('update-associate-dean');
    });
    Route::prefix('/administrative_class')->name('administrative_class.')->group(function () {
        Route::get('/list', [AdministrativeClassController::class, 'getListAdministrativeClass'])->name('list');
        Route::post('/create', [AdministrativeClassController::class, 'createAdministrativeClass'])->name('create');
        Route::post('/delete', [AdministrativeClassController::class, 'deleteAdministrativeClass'])->name('delete');
        Route::post('/import_student', [AdministrativeClassController::class, 'importStudent'])->name('import_student');
        Route::post('/delete_student', [AdministrativeClassController::class, 'deleteStudent'])->name('delete_student');
        Route::post('/delete_class', [AdministrativeClassController::class, 'deleteAdministrativeClass'])->name('delete_class');
    });
});
Route::group(['middleware' => []], function () {
    Route::prefix('subject-info')->group(function () {
        Route::get('/get-list-curriculum-period', [SubjectInfoController::class, 'getListCurriculumPeriod']);
        Route::get('/get-list-subject', [SubjectInfoController::class, 'getListSubject']);
    });

    Route::prefix('user')->group(function () {
        Route::get('/get-list-student-by-created-at', [UserApiController::class, 'getListStudentByCreatedAt']);
    });
});

Route::group(['middleware' => []], function () {
    Route::post('/ConfirmPaymentDng', [DngConnectController::class, 'getDngConnection']);
    Route::get('/getInvoiceControl', [DngConnectController::class, 'getInvoiceControl']);
});


// Fee Subject Management Routes
Route::group(['prefix' => 'fees', 'name' => 'fees.', 'middleware' => 'auth'], function () {
    Route::get('/fee-subjects', [FeeSubjectController::class, 'getFeeSubjects'])->name('fee_subjects');
    Route::get('/curriculums', [FeeSubjectController::class, 'getCurriculums'])->name('curriculums');
    Route::post('/fee-subjects/import', [FeeSubjectController::class, 'importFeeSubject'])->name('fee_subjects_import');
    Route::get('/fee-subjects/get-logs', [FeeSubjectController::class, 'getLogs'])->name('fee_subjects_get_logs');
    Route::get('/fee-subjects/download', [FeeSubjectController::class, 'downloadFeeSubject'])->name('fee_subjects_download');
});
Route::prefix('fee-types')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\FeeTypeController::class, 'index']);
    Route::get('/active', [App\Http\Controllers\Api\FeeTypeController::class, 'getActive']);
    Route::get('/key-value', [App\Http\Controllers\Api\FeeTypeController::class, 'getAsKeyValue']);
    Route::get('/{id}', [App\Http\Controllers\Api\FeeTypeController::class, 'show']);
    Route::post('/', [App\Http\Controllers\Api\FeeTypeController::class, 'store']);
    Route::put('/{id}', [App\Http\Controllers\Api\FeeTypeController::class, 'update']);
    Route::delete('/{id}', [App\Http\Controllers\Api\FeeTypeController::class, 'destroy']);
});


// Fee Subject Management Routes
Route::group(['prefix' => 'fees', 'middleware' => 'auth'], function () {
    Route::get('/fee-subjects', [FeeSubjectController::class, 'getFeeSubjects']);
    Route::get('/curriculums', [FeeSubjectController::class, 'getCurriculums']);
    Route::post('/fee-subjects/import', [FeeSubjectController::class, 'importFeeSubject']);
    Route::get('/fee-subjects/get-logs', [FeeSubjectController::class, 'getLogs']);
    Route::get('/fee-subjects/download', [FeeSubjectController::class, 'downloadFeeSubject']);
});

// Debt Management API routes
Route::prefix('debts')->name('debts.')->group(function () {
    // Basic CRUD operations
    Route::get('/', [App\Http\Controllers\Admin\DebtController::class, 'list']);
    Route::post('/create', [App\Http\Controllers\Admin\DebtController::class, 'create']);
    Route::get('/{id}/detail', [App\Http\Controllers\Admin\DebtController::class, 'getDetailData']);
    Route::post('/{id}/cancel', [App\Http\Controllers\Admin\DebtController::class, 'cancel']);

    // Payment operations
    Route::get('/{id}/payment', [App\Http\Controllers\Admin\DebtController::class, 'paymentForm']);
    Route::post('/{id}/payment', [App\Http\Controllers\Admin\DebtController::class, 'payment']);

    // Export operations
    Route::get('/export', [App\Http\Controllers\Admin\DebtController::class, 'export']);

    // Import operations (simplified)
    Route::post('/import', [App\Http\Controllers\Admin\DebtController::class, 'import']);
    Route::get('/import/template', [App\Http\Controllers\Admin\DebtController::class, 'downloadTemplate']);
    Route::get('/import/sample', [App\Http\Controllers\Admin\DebtController::class, 'downloadSample']);
    Route::get('/import/reference-data', [App\Http\Controllers\Admin\DebtController::class, 'getReferenceData']);

    // Debug/utility
    Route::get('/check-data', [App\Http\Controllers\Admin\DebtController::class, 'checkDebtData']);
});

// Logging API routes
Route::prefix('logs')->name('logs.')->group(function () {
    // Debt activity logs
    Route::get('/debts', [App\Http\Controllers\Admin\DebtLogController::class, 'getDebtLogs']);
    Route::get('/debts/{id}', [App\Http\Controllers\Admin\DebtLogController::class, 'getDebtDetailLogs']);

    // Wallet activity logs
    Route::get('/wallets', [App\Http\Controllers\Admin\DebtLogController::class, 'getWalletLogs']);
    Route::get('/wallets/{id}', [App\Http\Controllers\Admin\DebtLogController::class, 'getWalletDetailLogs']);

    // Statistics
    Route::get('/statistics', [App\Http\Controllers\Admin\DebtLogController::class, 'getLogStatistics']);
});

// Admission Management API (without any middleware for testing)
Route::prefix('admissions')->name('api.admissions.')->group(function () {
    Route::get('/', 'Admin\AdmissionController@getAdmissions')->name('list');

    Route::get('/download-template', 'Admin\AdmissionController@downloadTemplate')->name('download_template');
    Route::get('/export-data', 'Admin\AdmissionController@exportData')->name('export_data');
    Route::post('/create', 'Admin\AdmissionController@store')->name('store');
    Route::post('/bulk-approve', 'Admin\AdmissionController@bulkApprove')->name('bulk_approve');
    Route::post('/import', 'Admin\AdmissionController@import')->name('import');
    Route::get('/{id}', 'Admin\AdmissionController@show')->name('show');
    Route::put('/{id}', 'Admin\AdmissionController@update')->name('update');
    Route::delete('/{id}', 'Admin\AdmissionController@destroy')->name('destroy');
    Route::post('/{id}/approve', 'Admin\AdmissionController@approve')->name('approve');

    Route::post('/{id}/reject', 'Admin\AdmissionController@reject')->name('reject');


});